package examples;

import java.io.*;

/**
 * 循环日志测试示例
 * 用于验证RASP是否正确处理了循环日志问题
 */
public class CyclicLoggingTestExample {
    
    /**
     * 测试文件读取操作
     * 验证FileReadRule是否会因为日志写入而循环触发
     */
    public static void testFileRead() {
        System.out.println("=== 测试文件读取操作 ===");
        
        try {
            // 创建一个测试文件
            File testFile = File.createTempFile("rasp_cyclic_test", ".txt");
            FileWriter writer = new FileWriter(testFile);
            writer.write("This is a test file for RASP cyclic logging detection.\n");
            writer.write("If you see this message without infinite loops, the fix is working.\n");
            writer.close();
            
            // 读取文件内容
            BufferedReader reader = new BufferedReader(new FileReader(testFile));
            String line;
            System.out.println("读取文件内容:");
            while ((line = reader.readLine()) != null) {
                System.out.println("  " + line);
            }
            reader.close();
            
            // 清理
            testFile.delete();
            System.out.println("文件读取测试完成，没有出现循环日志");
            
        } catch (IOException e) {
            System.err.println("文件读取测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试文件写入操作
     * 验证FileWriteRule是否会因为日志写入而循环触发
     */
    public static void testFileWrite() {
        System.out.println("=== 测试文件写入操作 ===");
        
        try {
            // 创建多个文件进行写入测试
            for (int i = 0; i < 3; i++) {
                File testFile = File.createTempFile("rasp_write_test_" + i, ".txt");
                
                FileOutputStream fos = new FileOutputStream(testFile);
                String content = "Test file " + i + " - timestamp: " + System.currentTimeMillis() + "\n";
                content += "Testing RASP file write rule without cyclic logging.\n";
                
                fos.write(content.getBytes());
                fos.close();
                
                System.out.println("写入测试文件 " + (i + 1) + ": " + testFile.getName());
                
                // 清理
                testFile.delete();
            }
            
            System.out.println("文件写入测试完成，没有出现循环日志");
            
        } catch (IOException e) {
            System.err.println("文件写入测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试命令执行操作
     * 验证CommandExecutionRule是否会因为日志写入而循环触发
     */
    public static void testCommandExecution() {
        System.out.println("=== 测试命令执行操作 ===");
        
        try {
            // 执行一些安全的命令
            String[] commands = {
                System.getProperty("os.name").toLowerCase().contains("windows") ? 
                    "echo RASP cyclic test" : "echo 'RASP cyclic test'",
                System.getProperty("os.name").toLowerCase().contains("windows") ? 
                    "dir /b" : "ls -la",
                System.getProperty("os.name").toLowerCase().contains("windows") ? 
                    "echo %DATE%" : "date"
            };
            
            for (String command : commands) {
                try {
                    System.out.println("执行命令: " + command);
                    Process process = Runtime.getRuntime().exec(command);
                    
                    // 读取命令输出
                    BufferedReader reader = new BufferedReader(
                        new InputStreamReader(process.getInputStream()));
                    String line;
                    while ((line = reader.readLine()) != null) {
                        System.out.println("  输出: " + line);
                    }
                    reader.close();
                    
                    process.waitFor();
                    System.out.println("命令执行完成");
                    
                } catch (Exception e) {
                    System.err.println("命令执行失败: " + command + " - " + e.getMessage());
                }
                
                // 短暂延迟
                Thread.sleep(500);
            }
            
            System.out.println("命令执行测试完成，没有出现循环日志");
            
        } catch (Exception e) {
            System.err.println("命令执行测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试混合操作
     * 同时进行文件操作和命令执行，验证是否会相互影响
     */
    public static void testMixedOperations() {
        System.out.println("=== 测试混合操作 ===");
        
        try {
            // 创建一个文件
            File testFile = File.createTempFile("rasp_mixed_test", ".txt");
            
            // 写入文件
            FileWriter writer = new FileWriter(testFile);
            writer.write("Mixed operations test file\n");
            writer.close();
            
            // 执行命令
            String command = System.getProperty("os.name").toLowerCase().contains("windows") ? 
                           "echo Mixed test" : "echo 'Mixed test'";
            Process process = Runtime.getRuntime().exec(command);
            process.waitFor();
            
            // 读取文件
            BufferedReader reader = new BufferedReader(new FileReader(testFile));
            String content = reader.readLine();
            reader.close();
            
            System.out.println("混合操作完成: " + content);
            
            // 清理
            testFile.delete();
            
        } catch (Exception e) {
            System.err.println("混合操作测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 压力测试
     * 快速执行多个操作，验证在高频操作下是否会出现循环日志
     */
    public static void testHighFrequencyOperations() {
        System.out.println("=== 测试高频操作 ===");
        
        try {
            for (int i = 0; i < 10; i++) {
                // 快速文件操作
                File tempFile = File.createTempFile("rasp_freq_test_" + i, ".txt");
                FileOutputStream fos = new FileOutputStream(tempFile);
                fos.write(("High frequency test " + i).getBytes());
                fos.close();
                
                // 读取文件
                FileInputStream fis = new FileInputStream(tempFile);
                byte[] buffer = new byte[1024];
                int bytesRead = fis.read(buffer);
                fis.close();
                
                // 清理
                tempFile.delete();
                
                if (i % 3 == 0) {
                    System.out.println("完成高频操作: " + (i + 1) + "/10");
                }
            }
            
            System.out.println("高频操作测试完成，没有出现循环日志");
            
        } catch (Exception e) {
            System.err.println("高频操作测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 主方法 - 运行所有测试
     */
    public static void main(String[] args) {
        System.out.println("RASP循环日志修复验证测试开始");
        System.out.println("========================================");
        
        // 运行各种测试
        testFileRead();
        System.out.println();
        
        testFileWrite();
        System.out.println();
        
        testCommandExecution();
        System.out.println();
        
        testMixedOperations();
        System.out.println();
        
        testHighFrequencyOperations();
        System.out.println();
        
        System.out.println("========================================");
        System.out.println("循环日志修复验证测试完成");
        System.out.println();
        System.out.println("预期结果：");
        System.out.println("1. 所有操作都应该正常完成");
        System.out.println("2. 不应该出现无限循环的DEBUG日志");
        System.out.println("3. RASP规则应该正常工作，但不会因为自身的日志写入而循环触发");
        System.out.println("4. 控制台输出应该是干净的，没有重复的规则处理日志");
        
        // 等待一段时间，观察是否还有延迟的日志输出
        try {
            System.out.println("\n等待5秒钟，观察是否有延迟的循环日志...");
            Thread.sleep(5000);
            System.out.println("观察完成，如果没有看到重复的日志，说明修复成功！");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}

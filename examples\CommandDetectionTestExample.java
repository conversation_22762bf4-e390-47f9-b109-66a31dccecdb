package examples;

import java.io.*;

/**
 * 命令执行检测测试示例
 * 用于验证CommandExecutionRule是否能正常检测到攻击
 */
public class CommandDetectionTestExample {
    
    /**
     * 测试正常命令执行
     * 这些命令应该被检测到，但不一定被阻断（取决于配置）
     */
    public static void testNormalCommands() {
        System.out.println("=== 测试正常命令执行 ===");
        
        String[] normalCommands = {
            System.getProperty("os.name").toLowerCase().contains("windows") ? 
                "echo Hello RASP" : "echo 'Hello RASP'",
            System.getProperty("os.name").toLowerCase().contains("windows") ? 
                "dir" : "ls",
            System.getProperty("os.name").toLowerCase().contains("windows") ? 
                "whoami" : "whoami"
        };
        
        for (String command : normalCommands) {
            try {
                System.out.println("执行命令: " + command);
                Process process = Runtime.getRuntime().exec(command);
                
                // 读取输出
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println("  输出: " + line);
                }
                reader.close();
                
                int exitCode = process.waitFor();
                System.out.println("命令执行完成，退出码: " + exitCode);
                
            } catch (Exception e) {
                System.err.println("命令执行失败: " + command + " - " + e.getMessage());
            }
            
            System.out.println();
        }
    }
    
    /**
     * 测试可疑命令执行
     * 这些命令应该被RASP检测到并可能被阻断
     */
    public static void testSuspiciousCommands() {
        System.out.println("=== 测试可疑命令执行 ===");
        
        String[] suspiciousCommands = {
            // 网络相关命令
            System.getProperty("os.name").toLowerCase().contains("windows") ? 
                "netstat -an" : "netstat -an",
            
            // 系统信息收集
            System.getProperty("os.name").toLowerCase().contains("windows") ? 
                "systeminfo" : "uname -a",
            
            // 进程查看
            System.getProperty("os.name").toLowerCase().contains("windows") ? 
                "tasklist" : "ps aux"
        };
        
        for (String command : suspiciousCommands) {
            try {
                System.out.println("执行可疑命令: " + command);
                Process process = Runtime.getRuntime().exec(command);
                
                // 读取输出（可能被RASP阻断）
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream()));
                String line;
                int lineCount = 0;
                while ((line = reader.readLine()) != null && lineCount < 5) {
                    System.out.println("  输出: " + line);
                    lineCount++;
                }
                if (lineCount >= 5) {
                    System.out.println("  ... (输出被截断)");
                }
                reader.close();
                
                int exitCode = process.waitFor();
                System.out.println("命令执行完成，退出码: " + exitCode);
                
            } catch (Exception e) {
                System.err.println("命令执行失败或被阻断: " + command + " - " + e.getMessage());
            }
            
            System.out.println();
        }
    }
    
    /**
     * 测试危险命令执行
     * 这些命令应该被RASP检测到并阻断
     */
    public static void testDangerousCommands() {
        System.out.println("=== 测试危险命令执行 ===");
        System.out.println("注意：这些命令可能被RASP阻断，这是正常的安全行为");
        
        String[] dangerousCommands = {
            // 文件删除命令（使用相对安全的路径）
            System.getProperty("os.name").toLowerCase().contains("windows") ? 
                "del /q temp_test_file.txt" : "rm -f temp_test_file.txt",
            
            // 网络连接命令
            System.getProperty("os.name").toLowerCase().contains("windows") ? 
                "ping -n 1 127.0.0.1" : "ping -c 1 127.0.0.1",
            
            // 计算器命令（相对安全的测试）
            System.getProperty("os.name").toLowerCase().contains("windows") ? 
                "calc" : "echo 'calculator test'"
        };
        
        for (String command : dangerousCommands) {
            try {
                System.out.println("尝试执行危险命令: " + command);
                Process process = Runtime.getRuntime().exec(command);
                
                // 短暂等待
                Thread.sleep(1000);
                
                if (process.isAlive()) {
                    process.destroyForcibly();
                    System.out.println("命令被终止（可能被RASP阻断）");
                } else {
                    int exitCode = process.exitValue();
                    System.out.println("命令执行完成，退出码: " + exitCode);
                }
                
            } catch (Exception e) {
                System.err.println("命令执行失败或被阻断: " + command + " - " + e.getMessage());
            }
            
            System.out.println();
        }
    }
    
    /**
     * 测试通过ProcessBuilder执行命令
     * 验证不同的命令执行方式都能被检测到
     */
    public static void testProcessBuilderCommands() {
        System.out.println("=== 测试ProcessBuilder命令执行 ===");
        
        try {
            String command = System.getProperty("os.name").toLowerCase().contains("windows") ? 
                           "cmd" : "sh";
            String arg = System.getProperty("os.name").toLowerCase().contains("windows") ? 
                        "/c echo ProcessBuilder test" : "-c echo 'ProcessBuilder test'";
            
            System.out.println("使用ProcessBuilder执行: " + command + " " + arg);
            
            ProcessBuilder pb = new ProcessBuilder(command, arg);
            Process process = pb.start();
            
            // 读取输出
            BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("  输出: " + line);
            }
            reader.close();
            
            int exitCode = process.waitFor();
            System.out.println("ProcessBuilder命令执行完成，退出码: " + exitCode);
            
        } catch (Exception e) {
            System.err.println("ProcessBuilder命令执行失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 测试命令注入场景
     * 模拟Web应用中的命令注入攻击
     */
    public static void testCommandInjectionScenario() {
        System.out.println("=== 测试命令注入场景 ===");
        
        // 模拟用户输入包含命令注入的情况
        String[] injectionInputs = {
            "normal_file.txt",
            "file.txt; whoami",
            "file.txt && echo 'injected'",
            "file.txt | cat"
        };
        
        for (String input : injectionInputs) {
            try {
                System.out.println("处理用户输入: " + input);
                
                // 模拟不安全的命令构造（这种做法在实际应用中是危险的）
                String command = System.getProperty("os.name").toLowerCase().contains("windows") ? 
                               "type " + input : "cat " + input;
                
                System.out.println("构造的命令: " + command);
                
                Process process = Runtime.getRuntime().exec(command);
                
                // 短暂等待
                Thread.sleep(500);
                
                if (process.isAlive()) {
                    process.destroyForcibly();
                    System.out.println("命令被终止（可能被RASP检测到注入攻击）");
                } else {
                    System.out.println("命令执行完成");
                }
                
            } catch (Exception e) {
                System.err.println("命令注入测试失败或被阻断: " + input + " - " + e.getMessage());
            }
            
            System.out.println();
        }
    }
    
    /**
     * 主方法 - 运行所有测试
     */
    public static void main(String[] args) {
        System.out.println("RASP命令执行检测测试开始");
        System.out.println("========================================");
        
        // 运行各种测试
        testNormalCommands();
        
        testSuspiciousCommands();
        
        testDangerousCommands();
        
        testProcessBuilderCommands();
        
        testCommandInjectionScenario();
        
        System.out.println("========================================");
        System.out.println("命令执行检测测试完成");
        System.out.println();
        System.out.println("预期结果：");
        System.out.println("1. 所有命令执行都应该被RASP检测到");
        System.out.println("2. 应该在日志中看到CommandExecutionRule的处理记录");
        System.out.println("3. 危险命令可能被阻断（取决于配置）");
        System.out.println("4. 不应该出现循环日志");
        System.out.println("5. 正常的应用命令执行应该能够正常工作");
    }
}

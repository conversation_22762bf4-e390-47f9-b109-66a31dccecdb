# RASP反序列化攻击检测功能实现总结

## 功能概述

根据您的需求，我们实现了一个基于调用栈回溯的反序列化攻击检测机制。该功能**不需要新的Hook**，而是通过分析现有Hook（CommandExecutionHook、FileWriteHook、FileReadHook）触发时的调用栈，检测是否包含预定义的危险反序列化方法。

## 实现架构

### 核心组件

1. **DeserializationStackTraceRule** - 主要检测规则
   - 位置：`rasp-rules/src/main/java/com/rasp/rules/deserialization/DeserializationStackTraceRule.java`
   - 功能：当命令执行或文件操作发生时，分析调用栈中是否包含危险的反序列化方法

2. **黑名单方法库** - 内置在规则中
   - 包含30+种危险反序列化方法
   - 按风险等级分类（LOW、MEDIUM、HIGH、CRITICAL）
   - 支持自定义扩展

3. **配置管理** - 支持灵活配置
   - 配置文件：`rasp-deserialization-config.properties`
   - 支持启用/禁用、风险等级阈值设置

## 检测原理

```
触发事件 → 获取调用栈 → 遍历栈帧 → 匹配黑名单 → 评估风险 → 决定动作
```

### 检测流程

1. **事件触发**：CommandExecutionHook、FileWriteHook、FileReadHook触发
2. **调用栈分析**：获取当前线程的完整调用栈
3. **黑名单匹配**：检查栈中每个方法是否在危险方法黑名单中
4. **风险评估**：根据匹配到的方法确定风险等级
5. **动作决策**：根据配置决定是记录日志还是阻断执行

## 支持的攻击类型

### 1. Java原生反序列化攻击 (HIGH风险)
- `java.io.ObjectInputStream.readObject`
- `java.io.ObjectInputStream.readUnshared`
- `java.io.ObjectInputStream.defaultReadObject`

### 2. Apache Commons Collections攻击 (CRITICAL风险)
- `org.apache.commons.collections.functors.InvokerTransformer.transform`
- `org.apache.commons.collections.functors.InstantiateTransformer.transform`
- `org.apache.commons.collections4.functors.InvokerTransformer.transform`

### 3. JSON反序列化攻击 (HIGH风险)
- `com.alibaba.fastjson.JSON.parseObject`
- `com.alibaba.fastjson.JSON.parse`
- `com.alibaba.fastjson2.JSON.parseObject`

### 4. 其他反序列化框架
- Jackson：`com.fasterxml.jackson.databind.ObjectMapper.readValue` (MEDIUM)
- XStream：`com.thoughtworks.xstream.XStream.fromXML` (HIGH)
- Kryo：`com.esotericsoftware.kryo.Kryo.readObject` (MEDIUM)
- Hessian：`com.caucho.hessian.io.HessianInput.readObject` (MEDIUM)

### 5. 反射和其他危险方法
- `java.lang.reflect.Method.invoke` (MEDIUM)
- `java.lang.Class.newInstance` (MEDIUM)
- `javax.naming.InitialContext.lookup` (HIGH)

## 配置选项

### 基本配置
```properties
# 启用反序列化检测规则
deserialization.rule.enabled=true

# 启用阻断模式
deserialization.blocking.enabled=true

# 最低阻断风险等级 (LOW, MEDIUM, HIGH, CRITICAL)
deserialization.min.block.risk.level=HIGH
```

### 风险等级说明
- **LOW**：低风险，通常不阻断
- **MEDIUM**：中等风险，可配置阻断
- **HIGH**：高风险，建议阻断
- **CRITICAL**：严重风险，强烈建议阻断

## 集成方式

### 自动注册
规则会自动被RASP核心引擎发现和注册，无需手动配置：

1. **包扫描**：`com.rasp.rules.deserialization`包已添加到自动扫描列表
2. **默认启用**：规则在开发和生产模式下都默认启用
3. **配置管理**：支持通过配置文件动态启用/禁用

### 代码集成
```java
// 规则会自动注册，也可以手动配置
DeserializationStackTraceRule rule = new DeserializationStackTraceRule();

// 自定义配置
rule.setBlockingEnabled(true);
rule.setMinBlockRiskLevel(RuleResult.RiskLevel.HIGH);

// 添加自定义危险方法
rule.addCustomDangerousMethod("com.example.CustomDeserializer.deserialize", 
                              RuleResult.RiskLevel.HIGH);
```

## 检测示例

### 示例1：Commons Collections攻击
```java
// 当这个调用栈出现时：
// java.lang.Runtime.exec(Runtime.java:620)
// org.apache.commons.collections.functors.InvokerTransformer.transform(InvokerTransformer.java:125)
// java.io.ObjectInputStream.readObject(ObjectInputStream.java:431)

// RASP检测结果：
// SECURITY ALERT: Potential Deserialization Attack Detected
// Trigger Event: COMMAND_EXECUTION
// Dangerous Method: org.apache.commons.collections.functors.InvokerTransformer.transform
// Risk Level: CRITICAL
// Action: BLOCKED
```

### 示例2：Fastjson攻击
```java
// 当这个调用栈出现时：
// java.io.FileOutputStream.write(FileOutputStream.java:326)
// com.example.MaliciousClass.setFile(MaliciousClass.java:20)
// com.alibaba.fastjson.JSON.parseObject(JSON.java:350)

// RASP检测结果：
// SECURITY ALERT: Potential Deserialization Attack Detected
// Trigger Event: FILE_WRITE
// Dangerous Method: com.alibaba.fastjson.JSON.parseObject
// Risk Level: HIGH
// Action: BLOCKED
```

## 测试验证

### 测试用例
提供了完整的JUnit测试用例：
- `DeserializationStackTraceRuleTest.java`
- 覆盖各种攻击场景和配置选项

### 示例程序
提供了攻击模拟示例：
- `examples/DeserializationAttackExample.java`
- 包含多种攻击场景的模拟代码

## 性能考虑

### 开销分析
- **调用栈获取**：每次命令执行或文件操作都会获取调用栈
- **黑名单匹配**：使用HashSet，时间复杂度O(1)
- **内存占用**：黑名单和配置信息常驻内存，占用很小

### 优化措施
- 限制调用栈分析深度（默认50层）
- 使用高效的数据结构进行匹配
- 支持缓存机制减少重复计算

### 递归调用防护
RASP系统实现了多层递归调用防护机制：

#### 1. HookManager层面保护
- **ThreadLocal保护**：防止同一线程的递归Hook调用
- **内部包过滤**：自动过滤RASP内部类和日志框架类

#### 2. 规则层面保护
为了彻底解决循环日志问题，我们在各个规则中都添加了额外的过滤机制：

**FileReadRule/FileWriteRule过滤**：
```java
private boolean isInternalOrSystemOperation(FileEvent event) {
    // 检查调用栈中的RASP内部调用
    // 过滤日志框架调用
    // 跳过系统文件和JAR文件读取
    // 过滤Spring Boot Loader操作
}
```

**CommandExecutionRule过滤**：
```java
private boolean isInternalOperation(CommandExecutionEvent event) {
    // 智能检查：只有当调用栈中RASP内部调用占主导地位时才过滤
    // 确保正常的攻击检测不会被误过滤
    // 重点过滤RASP自身的测试和内部命令执行
}
```

**DeserializationStackTraceRule过滤**：
```java
private boolean isInternalOrFrameworkOperation(HookEvent event) {
    // 检查RASP内部操作
    // 过滤框架启动操作
    // 跳过系统文件读取
}
```

#### 3. 智能过滤策略
为了平衡循环日志防护和正常检测功能，我们采用了智能过滤策略：

**CommandExecutionRule**：
- 只有当调用栈中RASP内部调用占主导地位时才过滤
- 确保正常的攻击检测不会被误过滤
- 重点过滤RASP自身的测试和内部命令执行

**FileReadRule/FileWriteRule**：
- 基于调用栈比例进行智能判断
- 优先过滤系统文件和日志文件操作
- 保留对敏感文件访问的检测能力

**DeserializationStackTraceRule**：
- 多层验证机制，确保只过滤真正的内部操作
- 保持对反序列化攻击的高精度检测

#### 4. 解决的问题
- ✅ **循环日志**：规则写日志时不会再次触发自身
- ✅ **Spring Boot启动误报**：过滤应用启动时的正常文件操作
- ✅ **JAR文件读取误报**：跳过类加载时的JAR文件读取
- ✅ **系统操作误报**：过滤JVM内部和框架相关操作
- ✅ **保持检测能力**：确保正常的攻击检测功能不受影响

## 日志和监控

### 安全告警日志
```
2024-01-15 10:30:45 WARN SECURITY.DeserializationStackTraceRule - === POTENTIAL DESERIALIZATION ATTACK ===
2024-01-15 10:30:45 WARN SECURITY.DeserializationStackTraceRule - Trigger: COMMAND_EXECUTION -> org.apache.commons.collections.functors.InvokerTransformer.transform
2024-01-15 10:30:45 WARN SECURITY.DeserializationStackTraceRule - Risk Level: CRITICAL
2024-01-15 10:30:45 WARN SECURITY.DeserializationStackTraceRule - Details: [详细信息]
```

### 统计信息
- 黑名单方法数量统计
- 风险等级分布统计
- 检测和阻断次数统计

## 扩展能力

### 自定义黑名单
```java
// 添加自定义危险方法
rule.addCustomDangerousMethod("com.example.CustomDeserializer.deserialize", 
                              RuleResult.RiskLevel.HIGH);

// 通过配置文件添加
custom.dangerous.method.1=com.example.CustomDeserializer.deserialize=HIGH
```

### 配置灵活性
- 支持动态启用/禁用
- 支持风险等级阈值调整
- 支持阻断模式切换

## 总结

我们成功实现了一个**零侵入、高精度**的反序列化攻击检测机制：

✅ **无需新Hook**：复用现有的CommandExecutionHook和FileWriteHook
✅ **调用栈回溯**：智能分析调用栈中的危险方法
✅ **全面覆盖**：支持Java原生、Fastjson、Commons Collections等多种攻击
✅ **可配置**：灵活的风险等级和阻断策略
✅ **高性能**：优化的匹配算法，最小化性能影响
✅ **易扩展**：支持自定义黑名单和配置
✅ **完整测试**：提供测试用例和示例程序

该功能与现有RASP架构完美集成，为应用提供了强大的反序列化攻击防护能力。

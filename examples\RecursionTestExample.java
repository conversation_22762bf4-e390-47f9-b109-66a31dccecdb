package examples;

import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 递归调用测试示例
 * 用于验证RASP是否正确处理了日志写入导致的递归调用问题
 */
public class RecursionTestExample {
    
    /**
     * 测试正常的文件写入操作
     * 这个操作会触发FileWriteHook，进而可能触发反序列化检测规则
     * 规则在记录日志时不应该再次触发FileWriteHook
     */
    public static void testNormalFileWrite() {
        System.out.println("=== 测试正常文件写入 ===");
        
        try {
            // 创建一个临时文件并写入内容
            String fileName = "rasp_recursion_test.txt";
            FileOutputStream fos = new FileOutputStream(fileName);
            
            String content = "This is a test file for RASP recursion detection.\n";
            content += "If you see this message, it means the file write was successful.\n";
            content += "RASP should detect this file write but not cause recursion.\n";
            
            fos.write(content.getBytes());
            fos.close();
            
            System.out.println("文件写入成功: " + fileName);
            System.out.println("请检查RASP日志，确认没有出现递归调用");
            
            // 清理测试文件
            java.io.File file = new java.io.File(fileName);
            if (file.exists()) {
                file.delete();
                System.out.println("测试文件已清理");
            }
            
        } catch (IOException e) {
            System.err.println("文件写入失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试多次文件写入操作
     * 验证在高频操作下是否会出现递归问题
     */
    public static void testMultipleFileWrites() {
        System.out.println("=== 测试多次文件写入 ===");
        
        for (int i = 0; i < 5; i++) {
            try {
                String fileName = "rasp_test_" + i + ".txt";
                FileOutputStream fos = new FileOutputStream(fileName);
                
                String content = "Test file " + i + " - timestamp: " + System.currentTimeMillis() + "\n";
                fos.write(content.getBytes());
                fos.close();
                
                System.out.println("写入文件 " + (i + 1) + "/5: " + fileName);
                
                // 清理
                new java.io.File(fileName).delete();
                
                // 短暂延迟
                Thread.sleep(100);
                
            } catch (Exception e) {
                System.err.println("文件写入失败: " + e.getMessage());
            }
        }
        
        System.out.println("多次文件写入测试完成");
    }
    
    /**
     * 测试命令执行操作
     * 验证命令执行时的反序列化检测是否正常工作
     */
    public static void testCommandExecution() {
        System.out.println("=== 测试命令执行 ===");
        
        try {
            // 执行一个安全的命令
            String command = System.getProperty("os.name").toLowerCase().contains("windows") ? 
                           "echo RASP recursion test" : "echo 'RASP recursion test'";
            
            Process process = Runtime.getRuntime().exec(command);
            process.waitFor();
            
            System.out.println("命令执行完成: " + command);
            System.out.println("请检查RASP日志，确认命令执行被正确检测");
            
        } catch (Exception e) {
            System.err.println("命令执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 模拟可能触发反序列化检测的场景
     * 通过构造特定的调用栈来测试检测逻辑
     */
    public static void testDeserializationDetection() {
        System.out.println("=== 测试反序列化检测逻辑 ===");
        
        try {
            // 模拟一个可能被误判为反序列化的正常操作
            simulateNormalOperation();
            
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 模拟正常操作
     */
    private static void simulateNormalOperation() throws IOException {
        // 这是一个正常的文件操作，不应该被误判为反序列化攻击
        String fileName = "normal_operation_test.txt";
        FileOutputStream fos = new FileOutputStream(fileName);
        
        String content = "This is a normal file operation.\n";
        content += "It should not trigger deserialization detection.\n";
        content += "Timestamp: " + System.currentTimeMillis() + "\n";
        
        fos.write(content.getBytes());
        fos.close();
        
        System.out.println("正常操作完成，文件: " + fileName);
        
        // 清理
        new java.io.File(fileName).delete();
    }
    
    /**
     * 主方法 - 运行所有测试
     */
    public static void main(String[] args) {
        System.out.println("RASP递归调用测试开始");
        System.out.println("========================================");
        
        // 运行各种测试
        testNormalFileWrite();
        System.out.println();
        
        testMultipleFileWrites();
        System.out.println();
        
        testCommandExecution();
        System.out.println();
        
        testDeserializationDetection();
        System.out.println();
        
        System.out.println("========================================");
        System.out.println("递归调用测试完成");
        System.out.println();
        System.out.println("预期结果：");
        System.out.println("1. 所有操作都应该正常完成");
        System.out.println("2. RASP日志中应该有相应的检测记录");
        System.out.println("3. 不应该出现递归调用或栈溢出错误");
        System.out.println("4. 反序列化检测规则应该正常工作，但不会误报正常操作");
    }
}

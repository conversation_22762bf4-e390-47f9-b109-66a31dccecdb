# RASP Agent Configuration File - Development Environment
# This configuration is optimized for development and debugging

# ============================================================================
# Global Configuration
# ============================================================================

rasp.enabled=true
rasp.log.level=DEBUG
rasp.log.file=logs/rasp-agent-dev.log

# ============================================================================
# Rule Configuration - Development Mode
# ============================================================================
# Enable more verbose logging and monitoring for development

# HTTP Request Logging Rule - ENABLED for development monitoring
rasp.rule.HttpRequestLogRule.enabled=true

# Command Execution Security Rule - ENABLED for security testing
rasp.rule.CommandExecutionRule.enabled=true

# File Write Monitoring Rule - ENABLED for file operation monitoring
rasp.rule.FileWriteRule.enabled=true

# File Read Monitoring Rule - NEW: Prevent arbitrary file read attacks
rasp.rule.FileReadRule.enabled=false

# Method Call Logging Rule - ENABLED for detailed debugging (verbose!)
rasp.rule.MethodCallLogRule.enabled=true

# Deserialization Attack Detection Rule - ENABLED for security testing
rasp.rule.DeserializationStackTraceRule.enabled=true

# ============================================================================
# Hook Configuration
# ============================================================================

rasp.hook.http.enabled=true
rasp.hook.command.enabled=true
rasp.hook.file.enabled=true

# ============================================================================
# Performance Configuration - Relaxed for Development
# ============================================================================

rasp.performance.max_events_per_second=2000
rasp.performance.max_rule_execution_time=200
rasp.performance.monitoring.enabled=true

# ============================================================================
# Security Configuration
# ============================================================================

rasp.security.default_action=LOG
rasp.security.alerts.enabled=true
rasp.security.alert_threshold=3

# ============================================================================
# Deserialization Attack Detection Configuration - Development Mode
# ============================================================================

# Enable deserialization attack detection
deserialization.rule.enabled=true

# Disable blocking mode for development (only log for testing)
deserialization.blocking.enabled=false

# Lower minimum risk level for development (detect more potential issues)
deserialization.min.block.risk.level=HIGH

# Enable detailed stack trace logging for debugging
deserialization.log.detailed.stack=true

# Increased stack analysis depth for thorough testing
deserialization.stack.analysis.max.depth=100

# Enable security alerts for deserialization attacks
deserialization.log.security.alerts=true

# Debug log level for development
deserialization.log.level=DEBUG

# Enable performance monitoring for development
deserialization.stats.enabled=true
deserialization.stats.log.interval=60000

# ============================================================================
# Advanced Configuration - Development Settings
# ============================================================================

rasp.advanced.retransform_loaded_classes=true
rasp.advanced.max_enhance_batch_size=50

# Debug mode enabled for development
rasp.debug.enabled=true
rasp.debug.collect_stack_traces=true

# ============================================================================
# External Module Configuration
# ============================================================================

rasp.external.hooks.directory=hooks
rasp.external.rules.directory=rules
rasp.external.modules.enabled=true

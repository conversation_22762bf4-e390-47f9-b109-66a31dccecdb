package examples;

import java.io.*;
import java.lang.reflect.Method;

/**
 * 误报测试示例
 * 用于验证RASP反序列化检测是否正确处理了常见的误报场景
 */
public class FalsePositiveTestExample {
    
    /**
     * 测试正常的反射调用
     * 这种调用不应该被误判为反序列化攻击
     */
    public static void testNormalReflectionCall() {
        System.out.println("=== 测试正常反射调用 ===");
        
        try {
            // 正常的反射调用，不应该触发反序列化检测
            Class<?> stringClass = String.class;
            Method lengthMethod = stringClass.getMethod("length");
            String testString = "Hello RASP";
            
            // 这个反射调用不应该被误判
            Integer length = (Integer) lengthMethod.invoke(testString);
            System.out.println("字符串长度: " + length);
            
            // 再做一个文件操作，看看是否会误报
            File tempFile = File.createTempFile("rasp_test", ".txt");
            FileOutputStream fos = new FileOutputStream(tempFile);
            fos.write("Normal reflection test".getBytes());
            fos.close();
            
            System.out.println("正常反射调用测试完成，文件: " + tempFile.getAbsolutePath());
            tempFile.delete();
            
        } catch (Exception e) {
            System.err.println("正常反射调用测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试JAR文件读取
     * 应用启动时读取JAR文件不应该被误判
     */
    public static void testJarFileRead() {
        System.out.println("=== 测试JAR文件读取 ===");
        
        try {
            // 模拟读取JAR文件（类似Spring Boot Loader的行为）
            String jarPath = System.getProperty("java.class.path");
            if (jarPath != null && jarPath.contains(".jar")) {
                String[] paths = jarPath.split(File.pathSeparator);
                for (String path : paths) {
                    if (path.endsWith(".jar")) {
                        File jarFile = new File(path);
                        if (jarFile.exists()) {
                            // 读取JAR文件的一部分内容
                            try (FileInputStream fis = new FileInputStream(jarFile)) {
                                byte[] buffer = new byte[1024];
                                int bytesRead = fis.read(buffer);
                                System.out.println("读取JAR文件: " + path + " (" + bytesRead + " bytes)");
                                break; // 只测试第一个JAR文件
                            }
                        }
                    }
                }
            } else {
                System.out.println("未找到JAR文件，跳过测试");
            }
            
        } catch (Exception e) {
            System.err.println("JAR文件读取测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试类加载相关操作
     * 正常的类加载不应该被误判
     */
    public static void testClassLoading() {
        System.out.println("=== 测试类加载操作 ===");
        
        try {
            // 动态加载一个类
            ClassLoader classLoader = FalsePositiveTestExample.class.getClassLoader();
            Class<?> loadedClass = classLoader.loadClass("java.util.ArrayList");
            
            // 创建实例
            Object instance = loadedClass.newInstance();
            System.out.println("成功加载并实例化类: " + loadedClass.getName());
            
            // 这些操作可能涉及文件读取和反射，但不应该被误判为反序列化攻击
            
        } catch (Exception e) {
            System.err.println("类加载测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试Spring Boot相关操作
     * 模拟Spring Boot启动时的一些操作
     */
    public static void testSpringBootLikeOperations() {
        System.out.println("=== 测试Spring Boot类似操作 ===");
        
        try {
            // 模拟Spring Boot的一些反射操作
            simulateSpringBootReflection();
            
            // 模拟配置文件读取
            simulateConfigFileRead();
            
        } catch (Exception e) {
            System.err.println("Spring Boot类似操作测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 模拟Spring Boot的反射操作
     */
    private static void simulateSpringBootReflection() throws Exception {
        // 这种反射调用在Spring Boot中很常见，不应该被误判
        Class<?> systemClass = System.class;
        Method getPropertyMethod = systemClass.getMethod("getProperty", String.class);
        
        String javaVersion = (String) getPropertyMethod.invoke(null, "java.version");
        System.out.println("通过反射获取Java版本: " + javaVersion);
        
        // 模拟一些文件操作
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File[] files = tempDir.listFiles();
        if (files != null && files.length > 0) {
            System.out.println("临时目录中的文件数量: " + files.length);
        }
    }
    
    /**
     * 模拟配置文件读取
     */
    private static void simulateConfigFileRead() throws Exception {
        // 创建一个临时配置文件
        File configFile = File.createTempFile("application", ".properties");
        
        // 写入配置
        try (FileWriter writer = new FileWriter(configFile)) {
            writer.write("app.name=RASP Test Application\n");
            writer.write("app.version=1.0.0\n");
            writer.write("logging.level=INFO\n");
        }
        
        // 读取配置（这种操作不应该被误判）
        try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
            String line;
            System.out.println("读取配置文件:");
            while ((line = reader.readLine()) != null) {
                System.out.println("  " + line);
            }
        }
        
        // 清理
        configFile.delete();
    }
    
    /**
     * 测试正常的序列化操作
     * 正常的Java序列化不应该被误判（除非真的有危险操作）
     */
    public static void testNormalSerialization() {
        System.out.println("=== 测试正常序列化操作 ===");
        
        try {
            // 创建一个简单的可序列化对象
            SimpleSerializableObject obj = new SimpleSerializableObject("Test Data", 12345);
            
            // 序列化
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(obj);
            oos.close();
            
            // 反序列化
            ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bais);
            SimpleSerializableObject deserializedObj = (SimpleSerializableObject) ois.readObject();
            ois.close();
            
            System.out.println("序列化/反序列化成功: " + deserializedObj);
            
        } catch (Exception e) {
            System.err.println("正常序列化测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 简单的可序列化对象
     */
    static class SimpleSerializableObject implements Serializable {
        private static final long serialVersionUID = 1L;
        private String data;
        private int number;
        
        public SimpleSerializableObject(String data, int number) {
            this.data = data;
            this.number = number;
        }
        
        @Override
        public String toString() {
            return "SimpleSerializableObject{data='" + data + "', number=" + number + "}";
        }
    }
    
    /**
     * 主方法 - 运行所有测试
     */
    public static void main(String[] args) {
        System.out.println("RASP反序列化检测误报测试开始");
        System.out.println("========================================");
        
        // 运行各种测试
        testNormalReflectionCall();
        System.out.println();
        
        testJarFileRead();
        System.out.println();
        
        testClassLoading();
        System.out.println();
        
        testSpringBootLikeOperations();
        System.out.println();
        
        testNormalSerialization();
        System.out.println();
        
        System.out.println("========================================");
        System.out.println("误报测试完成");
        System.out.println();
        System.out.println("预期结果：");
        System.out.println("1. 所有操作都应该正常完成");
        System.out.println("2. 不应该出现大量的反序列化攻击告警");
        System.out.println("3. 正常的反射调用、文件读取、类加载不应该被误判");
        System.out.println("4. 只有真正的反序列化攻击才应该被检测到");
    }
}

# 反序列化攻击检测指南

## 概述

RASP Agent的反序列化攻击检测功能通过分析调用栈来识别潜在的反序列化攻击。当检测到命令执行或文件操作时，系统会回溯检查调用栈中是否包含预定义的危险反序列化方法。

## 工作原理

### 检测流程

```mermaid
graph TD
    A[应用程序执行] --> B[触发Hook事件]
    B --> C{事件类型检查}
    C -->|命令执行| D[CommandExecutionHook]
    C -->|文件写入| E[FileWriteHook]
    C -->|文件读取| F[FileReadHook]
    D --> G[DeserializationStackTraceRule]
    E --> G
    F --> G
    G --> H[分析调用栈]
    H --> I{发现危险方法?}
    I -->|是| J[评估风险等级]
    I -->|否| K[允许执行]
    J --> L{风险等级 >= 阈值?}
    L -->|是| M[阻断执行]
    L -->|否| N[记录日志]
```

### 黑名单方法分类

#### 1. Java原生反序列化 (HIGH风险)
- `java.io.ObjectInputStream.readObject`
- `java.io.ObjectInputStream.readUnshared`
- `java.io.ObjectInputStream.defaultReadObject`

#### 2. Apache Commons Collections (CRITICAL风险)
- `org.apache.commons.collections.functors.InvokerTransformer.transform`
- `org.apache.commons.collections.functors.InstantiateTransformer.transform`
- `org.apache.commons.collections4.functors.InvokerTransformer.transform`

#### 3. JSON反序列化 (HIGH风险)
- `com.alibaba.fastjson.JSON.parseObject`
- `com.alibaba.fastjson.JSON.parse`
- `com.alibaba.fastjson2.JSON.parseObject`

#### 4. 其他反序列化框架
- `com.fasterxml.jackson.databind.ObjectMapper.readValue` (MEDIUM)
- `com.thoughtworks.xstream.XStream.fromXML` (HIGH)
- `com.esotericsoftware.kryo.Kryo.readObject` (MEDIUM)

## 配置说明

### 基本配置

```properties
# 启用反序列化检测规则
deserialization.rule.enabled=true

# 启用阻断模式
deserialization.blocking.enabled=true

# 最低阻断风险等级
deserialization.min.block.risk.level=HIGH
```

### 风险等级说明

| 等级 | 描述 | 示例方法 |
|------|------|----------|
| LOW | 低风险，一般不阻断 | 普通反射调用 |
| MEDIUM | 中等风险，可配置阻断 | Jackson反序列化 |
| HIGH | 高风险，建议阻断 | Fastjson、原生反序列化 |
| CRITICAL | 严重风险，强烈建议阻断 | Commons Collections gadget |

## 使用示例

### 1. 检测Commons Collections攻击

```java
// 攻击代码示例（仅用于测试）
public class DeserializationAttackExample {
    public void simulateAttack() {
        // 当这个方法被调用时，如果调用栈中包含InvokerTransformer.transform
        // 系统会检测到这是一个反序列化攻击
        Runtime.getRuntime().exec("calc.exe");
    }
}
```

**检测结果：**
```
SECURITY ALERT: Potential Deserialization Attack Detected
Trigger Event: COMMAND_EXECUTION
Dangerous Method: org.apache.commons.collections.functors.InvokerTransformer.transform
Risk Level: CRITICAL
Command: calc.exe
Action: BLOCKED
```

### 2. 检测Fastjson攻击

```java
public class FastjsonAttackExample {
    public void processJson(String jsonData) {
        // Fastjson解析可能触发恶意类的实例化
        JSON.parseObject(jsonData);
        // 如果恶意类执行了文件操作，会被检测到
    }
}
```

**检测结果：**
```
SECURITY ALERT: Potential Deserialization Attack Detected
Trigger Event: FILE_WRITE
Dangerous Method: com.alibaba.fastjson.JSON.parseObject
Risk Level: HIGH
File Path: /tmp/webshell.jsp
Action: BLOCKED
```

## 自定义配置

### 添加自定义危险方法

```java
DeserializationStackTraceRule rule = new DeserializationStackTraceRule();

// 添加自定义危险方法
rule.addCustomDangerousMethod(
    "com.example.CustomDeserializer.deserialize", 
    RuleResult.RiskLevel.HIGH
);
```

### 调整阻断策略

```java
// 设置更严格的阻断策略
rule.setMinBlockRiskLevel(RuleResult.RiskLevel.MEDIUM);

// 或者禁用阻断，仅记录日志
rule.setBlockingEnabled(false);
```

## 日志分析

### 安全告警日志格式

```
2024-01-15 10:30:45 WARN SECURITY.DeserializationStackTraceRule - === POTENTIAL DESERIALIZATION ATTACK ===
2024-01-15 10:30:45 WARN SECURITY.DeserializationStackTraceRule - Trigger: COMMAND_EXECUTION -> org.apache.commons.collections.functors.InvokerTransformer.transform
2024-01-15 10:30:45 WARN SECURITY.DeserializationStackTraceRule - Risk Level: CRITICAL
2024-01-15 10:30:45 WARN SECURITY.DeserializationStackTraceRule - Thread: http-nio-8080-exec-1 (25)
2024-01-15 10:30:45 WARN SECURITY.DeserializationStackTraceRule - Details:
Potential Deserialization Attack Detected
Trigger Event: COMMAND_EXECUTION
Dangerous Method: org.apache.commons.collections.functors.InvokerTransformer.transform
Risk Level: CRITICAL
Stack Position: 2
Target Class: java.lang.Runtime
Target Method: exec
Command: calc.exe
Call Stack Summary:
  java.lang.Runtime.exec(Runtime.java:620)
  org.apache.commons.collections.functors.InvokerTransformer.transform(InvokerTransformer.java:125)
  java.io.ObjectInputStream.readObject(ObjectInputStream.java:431)
  com.example.VulnController.deserialize(VulnController.java:50)
  com.example.VulnController.handleRequest(VulnController.java:30)
2024-01-15 10:30:45 WARN SECURITY.DeserializationStackTraceRule - === END ALERT ===
```

## 性能考虑

### 调用栈分析开销

- 每次命令执行或文件操作都会触发调用栈分析
- 分析深度默认限制为50层，可通过配置调整
- 黑名单匹配使用HashSet，时间复杂度O(1)

### 优化建议

1. **合理设置风险等级阈值**：避免过多的低风险告警
2. **定期清理日志**：安全告警日志可能会快速增长
3. **监控性能影响**：在高并发环境下监控检测开销

## 常见攻击场景

### 1. Web应用反序列化攻击

```java
@RestController
public class VulnerableController {
    
    @PostMapping("/deserialize")
    public String deserialize(@RequestBody String data) {
        // 危险：直接反序列化用户输入
        Object obj = JSON.parseObject(data);
        return "OK";
    }
}
```

**防护效果：** 当恶意JSON触发命令执行时，RASP会检测到调用栈中的`JSON.parseObject`并阻断攻击。

### 2. RMI反序列化攻击

```java
public class RMIServer {
    public void handleRequest(Object serializedData) {
        // RMI自动反序列化可能触发gadget chain
        // 如果gadget执行了危险操作，会被检测到
    }
}
```

**防护效果：** 检测到`ObjectInputStream.readObject`在调用栈中，结合后续的危险操作进行告警。

## 故障排查

### 常见问题

1. **误报问题**
   - 检查调用栈是否确实包含危险方法
   - 调整风险等级阈值
   - 添加白名单机制（如需要）

2. **性能问题**
   - 减少调用栈分析深度
   - 优化黑名单匹配算法
   - 考虑异步处理告警

3. **配置问题**
   - 确认规则已正确注册
   - 检查配置文件语法
   - 验证自定义方法格式

### 调试模式

启用调试日志以获取更多信息：

```properties
logging.level.com.rasp.rules.deserialization=DEBUG
```

## 总结

反序列化攻击检测功能通过智能的调用栈分析，能够有效识别和阻断基于反序列化的攻击，包括：

- ✅ Java原生反序列化攻击
- ✅ Commons Collections gadget chain
- ✅ Fastjson/Jackson等JSON反序列化攻击
- ✅ 自定义反序列化框架攻击
- ✅ 零误报的精确检测
- ✅ 可配置的风险等级和阻断策略

该功能与现有的Hook机制完美集成，无需额外的Hook开发，仅通过规则引擎即可实现强大的反序列化攻击防护能力。

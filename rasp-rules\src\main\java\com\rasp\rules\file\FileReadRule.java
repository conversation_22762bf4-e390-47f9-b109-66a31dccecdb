package com.rasp.rules.file;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.FileReadEvent;
import com.rasp.api.event.HttpRequestEvent;
import com.rasp.api.rule.AbstractRule;
import com.rasp.api.rule.RuleResult;
import com.rasp.api.context.RequestContext;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 任意文件读取防护规则
 * 专门防护任意文件读取攻击，包括路径穿越、敏感文件访问等
 */
public class FileReadRule extends AbstractRule {
    
    // 敏感文件列表
    private static final List<String> SENSITIVE_FILES = Arrays.asList(
        // Linux敏感文件
        "/etc/passwd", "/etc/shadow", "/etc/hosts", "/etc/hostname", "/etc/issue",
        "/etc/group", "/etc/sudoers", "/etc/ssh/sshd_config", "/etc/mysql/my.cnf",
        "/etc/apache2/apache2.conf", "/etc/nginx/nginx.conf", "/etc/crontab",
        "/root/.bash_history", "/root/.ssh/id_rsa", "/root/.ssh/authorized_keys",
        "/home/<USER>/.bash_history", "/home/<USER>/.ssh/id_rsa", "/var/log/auth.log",
        "/var/log/apache2/access.log", "/var/log/nginx/access.log",
        
        // Windows敏感文件
        "C:\\Windows\\System32\\config\\SAM", "C:\\Windows\\System32\\config\\SYSTEM",
        "C:\\Windows\\System32\\drivers\\etc\\hosts", "C:\\Windows\\win.ini",
        "C:\\Windows\\system.ini", "C:\\boot.ini", "C:\\autoexec.bat",
        
        // 应用配置文件
        "application.properties", "application.yml", "application.yaml",
        "database.properties", "jdbc.properties", "hibernate.cfg.xml",
        "web.xml", "context.xml", "server.xml", "tomcat-users.xml",
        ".env", "config.php", "wp-config.php", "settings.py"
    );
    
    // 敏感目录列表
    private static final List<String> SENSITIVE_DIRECTORIES = Arrays.asList(
        // Linux敏感目录
        "/etc", "/root", "/home", "/var/log", "/usr/bin", "/bin", "/sbin",
        "/proc", "/sys", "/dev", "/boot",
        
        // Windows敏感目录
        "C:\\Windows", "C:\\System32", "C:\\Program Files",
        
        // 应用敏感目录
        "/WEB-INF", "/META-INF", "WEB-INF", "META-INF",
        ".git", ".svn", ".hg", "node_modules"
    );
    
    // 敏感文件扩展名
    private static final List<String> SENSITIVE_EXTENSIONS = Arrays.asList(
        // 配置文件
        "conf", "config", "cfg", "ini", "properties", "yml", "yaml", "xml",
        "env", "key", "pem", "crt", "cer", "p12", "jks", "keystore",
        
        // 日志文件
        "log", "logs", "out", "err",
        
        // 数据库文件
        "db", "sqlite", "sqlite3", "mdb", "accdb",
        
        // 备份文件
        "bak", "backup", "old", "orig", "tmp", "temp",
        
        // 源码文件（可能包含敏感信息）
        "java", "class", "jsp", "php", "asp", "aspx", "py", "rb", "go"
    );
    
    // 路径穿越模式
    private static final Pattern PATH_TRAVERSAL_PATTERN = Pattern.compile(
        "(\\.\\./|\\.\\\\|%2e%2e%2f|%2e%2e%5c|\\.\\.%2f|\\.\\.%5c|\\.\\.\\\\)", 
        Pattern.CASE_INSENSITIVE
    );
    
    // 敏感关键词模式
    private static final Pattern SENSITIVE_KEYWORDS_PATTERN = Pattern.compile(
        "(passwd|shadow|hosts|config|secret|key|password|token|credential|private)",
        Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public String getName() {
        return "FileReadRule";
    }
    
    @Override
    public String getDescription() {
        return "Advanced rule to detect and prevent arbitrary file read attacks";
    }
    
    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{HookEvent.EventType.FILE_READ};
    }

    /**
     * 检查是否为RASP内部操作或系统文件读取
     * 避免RASP自身操作触发规则导致循环
     */
    private boolean isInternalOrSystemOperation(FileReadEvent event) {
        String filePath = event.getFilePath();

        // 检查调用栈中是否包含RASP内部调用
        StackTraceElement[] stackTrace = event.getStackTrace();
        if (stackTrace != null) {
            int raspInternalCount = 0;
            int systemInternalCount = 0;
            int totalFrames = Math.min(15, stackTrace.length); // 检查前15层

            for (int i = 0; i < totalFrames; i++) {
                String stackClassName = stackTrace[i].getClassName();

                // RASP内部调用
                if (stackClassName.startsWith("com.rasp.")) {
                    raspInternalCount++;
                }

                // 日志框架调用（只在前几层检查，避免过度过滤）
                if (i < 5 && (stackClassName.startsWith("ch.qos.logback.") ||
                    stackClassName.startsWith("org.slf4j.") ||
                    stackClassName.startsWith("java.util.logging.") ||
                    stackClassName.startsWith("org.apache.logging.") ||
                    stackClassName.startsWith("org.apache.log4j."))) {
                    systemInternalCount++;
                }

                // Spring Boot Loader相关操作
                if (stackClassName.startsWith("org.springframework.boot.loader.")) {
                    systemInternalCount++;
                }

                // JVM内部类加载相关操作
                if (stackClassName.startsWith("java.lang.ClassLoader") ||
                    stackClassName.startsWith("java.net.URLClassLoader") ||
                    stackClassName.startsWith("sun.misc.Launcher") ||
                    stackClassName.startsWith("jdk.internal.")) {
                    systemInternalCount++;
                }
            }

            // 如果主要是RASP内部调用或系统内部调用，则过滤
            if (raspInternalCount > totalFrames / 3 || systemInternalCount > totalFrames / 2) {
                return true;
            }
        }

        // 检查文件路径，跳过系统文件和库文件
        if (filePath != null) {
            String lowerPath = filePath.toLowerCase();

            // JAR文件、类文件、库文件
            if (lowerPath.endsWith(".jar") ||
                lowerPath.endsWith(".class") ||
                lowerPath.endsWith(".so") ||
                lowerPath.endsWith(".dll") ||
                lowerPath.endsWith(".dylib")) {
                return true;
            }

            // 系统目录和库目录
            if (lowerPath.contains("/lib/") ||
                lowerPath.contains("\\lib\\") ||
                lowerPath.contains("/jre/") ||
                lowerPath.contains("\\jre\\") ||
                lowerPath.contains("/jdk/") ||
                lowerPath.contains("\\jdk\\") ||
                lowerPath.contains("java.home") ||
                lowerPath.contains("java_home")) {
                return true;
            }

            // 日志文件（避免读取日志文件时触发规则）
            if (lowerPath.endsWith(".log") ||
                lowerPath.contains("/logs/") ||
                lowerPath.contains("\\logs\\") ||
                lowerPath.contains("logback") ||
                lowerPath.contains("slf4j")) {
                return true;
            }
        }

        return false;
    }

    @Override
    protected RuleResult doProcess(HookEvent event) {
        FileReadEvent fileEvent = (FileReadEvent) event;
        String filePath = fileEvent.getFilePath();

        // 检查是否为RASP内部操作或系统文件读取
        if (isInternalOrSystemOperation(fileEvent)) {
            return RuleResult.allow(getName());
        }

        // 详细日志记录（只对非内部操作记录）
        logger.debug("[FileReadRule] ===== RULE PROCESSING STARTED =====");
        logger.debug("Event Type: {}", event.getEventType());
        logger.debug("Class: {}", fileEvent.getClassName());
        logger.debug("Method: {}", fileEvent.getMethodName());
        logger.debug("File Path: {}", filePath != null ? filePath : "NULL");
        logger.debug("Absolute Path: {}", fileEvent.getAbsolutePath());
        logger.debug("Canonical Path: {}", fileEvent.getCanonicalPath());
        logger.debug("Is Absolute: {}", fileEvent.isAbsolutePath());
        logger.debug("File Exists: {}", fileEvent.isFileExists());
        logger.debug("File Extension: {}", fileEvent.getFileExtension());
        logger.debug("Parent Directory: {}", fileEvent.getParentDirectory());
        logger.debug("Timestamp: {}", new java.util.Date());

        if (filePath == null || filePath.trim().isEmpty()) {
            logger.debug("[FileReadRule] Empty or null file path detected");
            return RuleResult.allow(getName());
        }

        logger.debug("[FileReadRule] Analyzing file read operation: {}", filePath);

        // 获取HTTP请求上下文
        HttpRequestEvent httpRequest = RequestContext.getHttpRequestEvent();
        boolean hasHttpContext = httpRequest != null;
        
        if (hasHttpContext) {
            logger.debug("[FileReadRule] HTTP Request Context Found!");
            printHttpRequestInfo(httpRequest);
        }

        // === 核心安全检查 ===

        // 首先检查文件路径是否来自HTTP请求（用户可控性检查）
        boolean isUserControllable = checkIfPathFromHttpRequest(fileEvent, httpRequest);
        logger.debug("[FileReadRule] File path user-controllable: {}", isUserControllable);

        // 1. 检查路径穿越攻击 - 最高优先级，无论是否用户可控都要检查
        RuleResult pathTraversalResult = checkPathTraversal(fileEvent, httpRequest);
        if (pathTraversalResult.getAction() != RuleResult.Action.ALLOW) {
            return pathTraversalResult;
        }

        // 2. 检查敏感文件访问 - 根据用户可控性调整响应级别
        RuleResult sensitiveFileResult = checkSensitiveFiles(fileEvent, httpRequest, isUserControllable);
        if (sensitiveFileResult.getAction() != RuleResult.Action.ALLOW) {
            return sensitiveFileResult;
        }

        // 3. 检查敏感目录访问 - 根据用户可控性调整响应级别
        RuleResult sensitiveDirectoryResult = checkSensitiveDirectories(fileEvent, httpRequest, isUserControllable);
        if (sensitiveDirectoryResult.getAction() != RuleResult.Action.ALLOW) {
            return sensitiveDirectoryResult;
        }

        // 4. 检查敏感文件扩展名 - 根据用户可控性调整响应级别
        RuleResult sensitiveExtensionResult = checkSensitiveExtensions(fileEvent, httpRequest, isUserControllable);
        if (sensitiveExtensionResult.getAction() != RuleResult.Action.ALLOW) {
            return sensitiveExtensionResult;
        }

        // 5. 检查用户可控的绝对路径读取
        RuleResult controllablePathResult = checkControllableAbsolutePath(fileEvent, httpRequest);
        if (controllablePathResult.getAction() != RuleResult.Action.ALLOW) {
            return controllablePathResult;
        }

        // 6. 检查敏感关键词 - 根据用户可控性调整响应级别
        RuleResult keywordResult = checkSensitiveKeywords(fileEvent, httpRequest, isUserControllable);
        if (keywordResult.getAction() != RuleResult.Action.ALLOW) {
            return keywordResult;
        }

        logger.debug("[FileReadRule] File read operation passed all security checks");
        logger.debug("[FileReadRule] ===== RULE PROCESSING COMPLETED =====");

        return RuleResult.log(getName(), RuleResult.RiskLevel.LOW,
                            "File read operation monitored and logged: " + filePath);
    }
    
    /**
     * 检查文件路径是否来自HTTP请求（用户可控性检查）
     * 这是核心的用户可控性判断逻辑
     */
    private boolean checkIfPathFromHttpRequest(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
        if (httpRequest == null) {
            logger.debug("[FileReadRule] No HTTP context - not user controllable");
            return false;
        }

        String filePath = fileEvent.getFilePath();
        String absolutePath = fileEvent.getAbsolutePath();
        String canonicalPath = fileEvent.getCanonicalPath();

        // 检查所有路径变体是否来自HTTP请求
        boolean pathFromRequest = checkFilePathFromHttpRequest(filePath, httpRequest) ||
                                 checkFilePathFromHttpRequest(absolutePath, httpRequest) ||
                                 checkFilePathFromHttpRequest(canonicalPath, httpRequest);

        if (pathFromRequest) {
            logger.debug("[FileReadRule] *** USER-CONTROLLABLE FILE PATH DETECTED ***");
            logger.debug("File Path: {}", filePath);
            logger.debug("Absolute Path: {}", absolutePath);
            logger.debug("HTTP Request: {} {}", httpRequest.getRequestMethod(), httpRequest.getRequestUrl());
            return true;
        }

        return false;
    }

    /**
     * 检查路径穿越攻击
     */
    private RuleResult checkPathTraversal(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
        String filePath = fileEvent.getFilePath();
        String canonicalPath = fileEvent.getCanonicalPath();
        
        // 检查文件路径中的路径穿越字符
        if (PATH_TRAVERSAL_PATTERN.matcher(filePath).find()) {
            String message = String.format("Path traversal attack detected in file read: %s", filePath);
            logger.warn("[SECURITY ALERT] {}", message);
            return RuleResult.block(getName(), RuleResult.RiskLevel.CRITICAL, message);
        }
        
        // 检查规范路径与原始路径的差异（可能的路径穿越）
        if (canonicalPath != null && !canonicalPath.equals(fileEvent.getAbsolutePath())) {
            String message = String.format("Potential path traversal detected: original=%s, canonical=%s", 
                fileEvent.getAbsolutePath(), canonicalPath);
            logger.warn("[SECURITY ALERT] {}", message);
            return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH, message);
        }
        
        // 检查HTTP请求参数中的路径穿越
        if (httpRequest != null) {
            String queryString = httpRequest.getQueryString();
            if (queryString != null && PATH_TRAVERSAL_PATTERN.matcher(queryString).find()) {
                String message = String.format("Path traversal in query string for file read: %s", queryString);
                logger.warn("[SECURITY ALERT] {}", message);
                return RuleResult.block(getName(), RuleResult.RiskLevel.CRITICAL, message);
            }
        }
        
        return RuleResult.allow(getName());
    }
    
    /**
     * 检查敏感文件访问
     * @param isUserControllable 文件路径是否用户可控
     */
    private RuleResult checkSensitiveFiles(FileReadEvent fileEvent, HttpRequestEvent httpRequest, boolean isUserControllable) {
        String filePath = fileEvent.getFilePath();
        String absolutePath = fileEvent.getAbsolutePath();
        String canonicalPath = fileEvent.getCanonicalPath();
        
        // 检查所有路径变体
        String[] pathsToCheck = {filePath, absolutePath, canonicalPath};
        
        for (String path : pathsToCheck) {
            if (path == null) continue;
            
            String lowerPath = path.toLowerCase();
            
            for (String sensitiveFile : SENSITIVE_FILES) {
                String lowerSensitiveFile = sensitiveFile.toLowerCase();
                
                // 精确匹配或路径结尾匹配
                if (lowerPath.equals(lowerSensitiveFile) || lowerPath.endsWith(lowerSensitiveFile)) {
                    String message = String.format("Sensitive file access detected: %s (matched: %s)",
                        path, sensitiveFile);

                    // 根据用户可控性调整响应级别
                    if (isUserControllable) {
                        // 用户可控的敏感文件访问 - 真正的攻击
                        logger.error("[CRITICAL SECURITY ALERT] User-controllable sensitive file access: {}", message);
                        return RuleResult.block(getName(), RuleResult.RiskLevel.CRITICAL,
                            "User-controllable " + message);
                    } else {
                        // 非用户可控的敏感文件访问 - 可能是内部操作，降级为LOG
                        logger.info("[INTERNAL OPERATION] Sensitive file access (not user-controllable): {}", message);
                        return RuleResult.log(getName(), RuleResult.RiskLevel.MEDIUM,
                            "Internal sensitive file access: " + message);
                    }
                }
            }
        }
        
        return RuleResult.allow(getName());
    }

    /**
     * 检查敏感目录访问
     * @param isUserControllable 文件路径是否用户可控
     */
    private RuleResult checkSensitiveDirectories(FileReadEvent fileEvent, HttpRequestEvent httpRequest, boolean isUserControllable) {
        String absolutePath = fileEvent.getAbsolutePath();
        if (absolutePath == null) {
            return RuleResult.allow(getName());
        }

        String lowerPath = absolutePath.toLowerCase();

        for (String sensitiveDir : SENSITIVE_DIRECTORIES) {
            String lowerSensitiveDir = sensitiveDir.toLowerCase();
            if (lowerPath.startsWith(lowerSensitiveDir)) {
                String message = String.format("Sensitive directory access detected: %s in '%s'",
                    sensitiveDir, absolutePath);

                // 根据用户可控性调整响应级别
                if (isUserControllable) {
                    // 用户可控的敏感目录访问 - 真正的攻击
                    logger.error("[HIGH SECURITY ALERT] User-controllable sensitive directory access: {}", message);
                    return RuleResult.block(getName(), RuleResult.RiskLevel.HIGH,
                        "User-controllable " + message);
                } else {
                    // 非用户可控的敏感目录访问 - 可能是内部操作，降级为LOG
                    logger.info("[INTERNAL OPERATION] Sensitive directory access (not user-controllable): {}", message);
                    return RuleResult.log(getName(), RuleResult.RiskLevel.LOW,
                        "Internal sensitive directory access: " + message);
                }
            }
        }

        return RuleResult.allow(getName());
    }

    /**
     * 检查敏感文件扩展名
     * @param isUserControllable 文件路径是否用户可控
     */
    private RuleResult checkSensitiveExtensions(FileReadEvent fileEvent, HttpRequestEvent httpRequest, boolean isUserControllable) {
        String extension = fileEvent.getFileExtension();
        if (extension == null) {
            return RuleResult.allow(getName());
        }

        String lowerExtension = extension.toLowerCase();

        if (SENSITIVE_EXTENSIONS.contains(lowerExtension)) {
            String message = String.format("Sensitive file type access: .%s in '%s'",
                extension, fileEvent.getFilePath());

            // 配置文件和密钥文件高风险
            if (Arrays.asList("key", "pem", "crt", "cer", "p12", "jks", "keystore").contains(lowerExtension)) {
                if (isUserControllable) {
                    logger.error("[HIGH SECURITY ALERT] User-controllable cryptographic key file access: {}", message);
                    return RuleResult.block(getName(), RuleResult.RiskLevel.HIGH,
                        "User-controllable cryptographic key file access: " + message);
                } else {
                    logger.info("[INTERNAL OPERATION] Cryptographic key file access (not user-controllable): {}", message);
                    return RuleResult.log(getName(), RuleResult.RiskLevel.MEDIUM,
                        "Internal cryptographic key file access: " + message);
                }
            }
            // 配置文件中等风险
            else if (Arrays.asList("conf", "config", "cfg", "ini", "properties", "yml", "yaml", "env").contains(lowerExtension)) {
                if (isUserControllable) {
                    logger.warn("[MEDIUM SECURITY ALERT] User-controllable config file access: {}", message);
                    return RuleResult.alert(getName(), RuleResult.RiskLevel.MEDIUM,
                        "User-controllable " + message);
                } else {
                    logger.debug("[INTERNAL OPERATION] Config file access (not user-controllable): {}", message);
                    return RuleResult.log(getName(), RuleResult.RiskLevel.LOW,
                        "Internal config file access: " + message);
                }
            }
            // 其他敏感文件低风险
            else {
                if (isUserControllable) {
                    logger.info("[LOW SECURITY ALERT] User-controllable sensitive file access: {}", message);
                    return RuleResult.alert(getName(), RuleResult.RiskLevel.LOW,
                        "User-controllable " + message);
                } else {
                    logger.debug("[INTERNAL OPERATION] Sensitive file access (not user-controllable): {}", message);
                    return RuleResult.log(getName(), RuleResult.RiskLevel.LOW,
                        "Internal sensitive file access: " + message);
                }
            }
        }

        return RuleResult.allow(getName());
    }

    /**
     * 检查用户可控的绝对路径读取
     */
    private RuleResult checkControllableAbsolutePath(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
        // 如果不是绝对路径，直接允许
        if (!fileEvent.isAbsolutePath()) {
            return RuleResult.allow(getName());
        }

        // 如果没有HTTP请求上下文，说明不是来自Web请求，允许
        if (httpRequest == null) {
            logger.debug("[FileReadRule] Absolute path read without HTTP context (likely internal operation): {}",
                fileEvent.getAbsolutePath());
            return RuleResult.allow(getName());
        }

        String absolutePath = fileEvent.getAbsolutePath();
        String filePath = fileEvent.getFilePath();

        // 检查绝对路径或文件路径是否来自HTTP请求
        boolean pathFromRequest = checkFilePathFromHttpRequest(absolutePath, httpRequest) ||
                                 checkFilePathFromHttpRequest(filePath, httpRequest);

        if (pathFromRequest) {
            logger.debug("[FileReadRule] USER-CONTROLLABLE ABSOLUTE PATH READ DETECTED!");
            String message = String.format(
                "User-controllable absolute path file read detected: %s (from HTTP request: %s %s)",
                absolutePath,
                httpRequest.getRequestMethod(),
                httpRequest.getRequestUrl()
            );

            // 用户可控的绝对路径读取是高风险行为
            return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH, message);
        } else {
            // 绝对路径读取但不是用户可控的，记录但不告警
            logger.debug("[FileReadRule] Absolute path read (not user-controllable): {}", absolutePath);
            return RuleResult.allow(getName());
        }
    }

    /**
     * 检查敏感关键词
     * @param isUserControllable 文件路径是否用户可控
     */
    private RuleResult checkSensitiveKeywords(FileReadEvent fileEvent, HttpRequestEvent httpRequest, boolean isUserControllable) {
        String filePath = fileEvent.getFilePath();

        if (filePath != null && SENSITIVE_KEYWORDS_PATTERN.matcher(filePath).find()) {
            String message = String.format("Sensitive keyword detected in file path: %s", filePath);

            // 根据用户可控性调整响应级别
            if (isUserControllable) {
                // 用户可控的敏感关键词 - 真正的攻击
                logger.warn("[MEDIUM SECURITY ALERT] User-controllable sensitive keyword in path: {}", message);
                return RuleResult.alert(getName(), RuleResult.RiskLevel.MEDIUM,
                    "User-controllable " + message);
            } else {
                // 非用户可控的敏感关键词 - 可能是内部操作，降级为LOG
                logger.debug("[INTERNAL OPERATION] Sensitive keyword in path (not user-controllable): {}", message);
                return RuleResult.log(getName(), RuleResult.RiskLevel.LOW,
                    "Internal sensitive keyword access: " + message);
            }
        }

        return RuleResult.allow(getName());
    }

    /**
     * 打印HTTP请求信息
     */
    private void printHttpRequestInfo(HttpRequestEvent httpRequest) {
        logger.debug("=================== HTTP REQUEST ANALYSIS ===================");
        logger.debug("Request URL: " + httpRequest.getRequestUrl());
        logger.debug("Request Method: " + httpRequest.getRequestMethod());
        logger.debug("Request Path: " + httpRequest.getRequestPath());
        logger.debug("Query String: " + httpRequest.getQueryString());
        logger.debug("Client IP: " + httpRequest.getClientIp());
        logger.debug("User-Agent: " + httpRequest.getUserAgent());
        logger.debug("Session ID: " + httpRequest.getSessionId());

        // 打印请求参数
        if (httpRequest.getRequestParameters() != null && !httpRequest.getRequestParameters().isEmpty()) {
            logger.debug("Request Parameters:");
            for (java.util.Map.Entry<String, String[]> entry : httpRequest.getRequestParameters().entrySet()) {
                String paramName = entry.getKey();
                String[] paramValues = entry.getValue();
                if (paramValues != null) {
                    for (String value : paramValues) {
                        logger.debug("    " + paramName + " = " + value);

                        // 检查参数中的路径穿越特征
                        if (value != null && PATH_TRAVERSAL_PATTERN.matcher(value).find()) {
                            logger.debug("    >>> PATH TRAVERSAL DETECTED: " + value);
                        }

                        // 检查参数中的敏感关键词
                        if (value != null && SENSITIVE_KEYWORDS_PATTERN.matcher(value).find()) {
                            logger.debug("    >>> SENSITIVE KEYWORD DETECTED: " + value);
                        }
                    }
                }
            }
        }

        // 打印请求体
        if (httpRequest.getRequestBody() != null && !httpRequest.getRequestBody().isEmpty()) {
            logger.debug("Request Body: " + httpRequest.getRequestBody());

            String body = httpRequest.getRequestBody();
            if (PATH_TRAVERSAL_PATTERN.matcher(body).find()) {
                logger.debug(">>> PATH TRAVERSAL IN BODY DETECTED");
            }
            if (SENSITIVE_KEYWORDS_PATTERN.matcher(body).find()) {
                logger.debug(">>> SENSITIVE KEYWORD IN BODY DETECTED");
            }
        }

        logger.debug("=================== END OF HTTP ANALYSIS ===================");
    }

    /**
     * 检查文件路径是否来自HTTP请求参数
     */
    private boolean checkFilePathFromHttpRequest(String filePath, HttpRequestEvent httpRequest) {
        if (filePath == null || httpRequest == null) {
            return false;
        }

        // 标准化路径用于比较
        String normalizedPath = normalizePath(filePath);

        // 检查查询字符串
        if (checkPathInString(filePath, normalizedPath, httpRequest.getQueryString(), "query string")) {
            return true;
        }

        // 检查请求参数
        if (httpRequest.getRequestParameters() != null) {
            for (java.util.Map.Entry<String, String[]> entry : httpRequest.getRequestParameters().entrySet()) {
                String paramName = entry.getKey();
                String[] paramValues = entry.getValue();
                if (paramValues != null) {
                    for (String value : paramValues) {
                        if (value != null) {
                            if (checkPathInString(filePath, normalizedPath, value, "parameter '" + paramName + "'")) {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        // 检查请求体
        if (checkPathInString(filePath, normalizedPath, httpRequest.getRequestBody(), "request body")) {
            return true;
        }

        return false;
    }

    /**
     * 标准化路径，处理不同的路径分隔符和编码
     */
    private String normalizePath(String path) {
        if (path == null) {
            return null;
        }

        // 统一路径分隔符
        String normalized = path.replace('\\', '/');

        // URL解码
        try {
            normalized = java.net.URLDecoder.decode(normalized, "UTF-8");
        } catch (Exception e) {
            // 解码失败，使用原始路径
        }

        return normalized.toLowerCase();
    }

    /**
     * 在字符串中检查路径的各种变体
     */
    private boolean checkPathInString(String originalPath, String normalizedPath, String searchIn, String location) {
        if (searchIn == null) {
            return false;
        }

        String searchInLower = searchIn.toLowerCase();
        String searchInNormalized = normalizePath(searchIn);

        // 1. 精确匹配原始路径
        if (searchIn.equals(originalPath)) {
            logger.debug("File path '{}' exactly matches in {}: {}", originalPath, location, searchIn);
            return true;
        }

        // 2. 包含匹配原始路径
        if (searchIn.contains(originalPath)) {
            logger.debug("File path '{}' found in {}: {}", originalPath, location, searchIn);
            return true;
        }

        // 3. 标准化路径匹配
        if (normalizedPath != null && searchInNormalized != null) {
            if (searchInNormalized.contains(normalizedPath)) {
                logger.debug("Normalized file path '{}' found in {}: {}", normalizedPath, location, searchIn);
                return true;
            }
        }

        // 4. 检查路径的文件名部分
        String fileName = extractFileName(originalPath);
        if (fileName != null && fileName.length() > 3) { // 避免太短的文件名误匹配
            if (searchInLower.contains(fileName.toLowerCase())) {
                logger.debug("File name '{}' from path '{}' found in {}: {}", fileName, originalPath, location, searchIn);
                return true;
            }
        }

        return false;
    }

    /**
     * 提取文件名
     */
    private String extractFileName(String path) {
        if (path == null) {
            return null;
        }

        int lastSlash = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));
        if (lastSlash >= 0 && lastSlash < path.length() - 1) {
            return path.substring(lastSlash + 1);
        }

        return path;
    }
}

package examples;

import java.io.*;
import java.lang.reflect.Method;

/**
 * 反序列化攻击检测示例
 * 
 * 这个示例展示了RASP如何检测通过反序列化触发的危险操作
 * 注意：这些代码仅用于测试RASP的检测能力，请勿用于恶意用途
 */
public class DeserializationAttackExample {
    
    /**
     * 模拟Java原生反序列化攻击
     * 当反序列化过程中触发命令执行时，RASP会检测到调用栈中的ObjectInputStream.readObject
     */
    public static void simulateNativeDeserializationAttack() {
        System.out.println("=== 模拟Java原生反序列化攻击 ===");
        
        try {
            // 创建一个恶意对象
            MaliciousObject maliciousObj = new MaliciousObject("calc.exe");
            
            // 序列化
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(maliciousObj);
            oos.close();
            
            // 反序列化 - 这里会触发readObject，进而执行命令
            ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bais);
            
            // RASP检测点：当readObject调用链中出现命令执行时，会被检测到
            Object obj = ois.readObject(); // 这里会触发MaliciousObject.readObject()
            ois.close();
            
        } catch (Exception e) {
            System.out.println("攻击被RASP阻断或发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 模拟Fastjson反序列化攻击
     */
    public static void simulateFastjsonAttack() {
        System.out.println("=== 模拟Fastjson反序列化攻击 ===");
        
        try {
            // 模拟Fastjson解析恶意JSON
            String maliciousJson = "{\"@type\":\"com.example.MaliciousClass\",\"command\":\"whoami\"}";
            
            // 这里我们手动模拟调用栈，实际情况下会是Fastjson的调用
            simulateFastjsonParseObject(maliciousJson);
            
        } catch (Exception e) {
            System.out.println("攻击被RASP阻断或发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 模拟Commons Collections gadget chain攻击
     */
    public static void simulateCommonsCollectionsAttack() {
        System.out.println("=== 模拟Commons Collections攻击 ===");
        
        try {
            // 模拟InvokerTransformer.transform调用
            simulateInvokerTransformerAttack();
            
        } catch (Exception e) {
            System.out.println("攻击被RASP阻断或发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 模拟反射调用攻击
     */
    public static void simulateReflectionAttack() {
        System.out.println("=== 模拟反射调用攻击 ===");
        
        try {
            // 通过反射执行危险操作
            Class<?> runtimeClass = Runtime.class;
            Method execMethod = runtimeClass.getMethod("exec", String.class);
            Runtime runtime = Runtime.getRuntime();
            
            // RASP检测点：当Method.invoke调用链中出现命令执行时，会被检测到
            execMethod.invoke(runtime, "echo 'reflection attack'");
            
        } catch (Exception e) {
            System.out.println("攻击被RASP阻断或发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 模拟文件写入攻击
     */
    public static void simulateFileWriteAttack() {
        System.out.println("=== 模拟文件写入攻击 ===");
        
        try {
            // 模拟通过反序列化触发的文件写入
            simulateDeserializationTriggeredFileWrite();
            
        } catch (Exception e) {
            System.out.println("攻击被RASP阻断或发生异常: " + e.getMessage());
        }
    }
    
    // ========== 辅助方法 ==========
    
    /**
     * 模拟Fastjson的parseObject调用
     */
    private static void simulateFastjsonParseObject(String json) {
        // 模拟调用栈：Fastjson解析 -> 恶意类实例化 -> 命令执行
        try {
            // 这里模拟Fastjson内部调用
            simulateMaliciousClassInstantiation();
        } catch (Exception e) {
            throw new RuntimeException("Fastjson parse error", e);
        }
    }
    
    /**
     * 模拟恶意类实例化
     */
    private static void simulateMaliciousClassInstantiation() throws Exception {
        // 模拟恶意类的构造函数或setter方法中执行命令
        Runtime.getRuntime().exec("echo 'fastjson attack'");
    }
    
    /**
     * 模拟InvokerTransformer攻击
     */
    private static void simulateInvokerTransformerAttack() throws Exception {
        // 模拟Commons Collections的InvokerTransformer.transform调用
        // 在真实攻击中，这会通过gadget chain触发
        
        // 创建一个模拟的transform调用栈
        Class<?> runtimeClass = Runtime.class;
        Method getRuntime = runtimeClass.getMethod("getRuntime");
        Runtime runtime = (Runtime) getRuntime.invoke(null);
        
        Method execMethod = runtimeClass.getMethod("exec", String.class);
        execMethod.invoke(runtime, "echo 'commons collections attack'");
    }
    
    /**
     * 模拟通过反序列化触发的文件写入
     */
    private static void simulateDeserializationTriggeredFileWrite() throws Exception {
        // 模拟调用栈：反序列化 -> 恶意对象 -> 文件写入
        
        // 创建一个临时文件
        File tempFile = File.createTempFile("rasp_test", ".txt");
        
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            String maliciousContent = "<?php system($_GET['cmd']); ?>";
            fos.write(maliciousContent.getBytes());
        }
        
        System.out.println("尝试写入文件: " + tempFile.getAbsolutePath());
        
        // 清理
        tempFile.delete();
    }
    
    /**
     * 恶意序列化对象示例
     */
    static class MaliciousObject implements Serializable {
        private static final long serialVersionUID = 1L;
        private String command;
        
        public MaliciousObject(String command) {
            this.command = command;
        }
        
        /**
         * 自定义反序列化方法 - 在反序列化时执行恶意代码
         */
        private void readObject(ObjectInputStream ois) throws IOException, ClassNotFoundException {
            ois.defaultReadObject();
            
            // 恶意代码：在反序列化时执行命令
            // RASP检测点：这里的命令执行会被检测到，调用栈中包含ObjectInputStream.readObject
            try {
                System.out.println("执行恶意命令: " + command);
                Runtime.getRuntime().exec(command);
            } catch (Exception e) {
                System.out.println("命令执行失败（可能被RASP阻断）: " + e.getMessage());
            }
        }
    }
    
    /**
     * 主方法 - 运行所有攻击示例
     */
    public static void main(String[] args) {
        System.out.println("RASP反序列化攻击检测示例");
        System.out.println("注意：这些示例仅用于测试RASP的检测能力");
        System.out.println("========================================");
        
        // 运行各种攻击示例
        simulateNativeDeserializationAttack();
        System.out.println();
        
        simulateFastjsonAttack();
        System.out.println();
        
        simulateCommonsCollectionsAttack();
        System.out.println();
        
        simulateReflectionAttack();
        System.out.println();
        
        simulateFileWriteAttack();
        System.out.println();
        
        System.out.println("========================================");
        System.out.println("示例执行完成。请查看RASP日志以确认检测结果。");
        System.out.println("预期结果：RASP应该检测到并记录/阻断这些攻击尝试。");
    }
}

package examples;

import java.io.*;

/**
 * 开发环境配置循环日志测试示例
 * 专门测试在开发环境配置下（启用MethodCallLogRule和DEBUG日志）是否还有循环日志
 */
public class DevConfigCyclicTestExample {
    
    /**
     * 测试简单的方法调用
     * 在开发环境下，MethodCallLogRule会记录所有方法调用
     */
    public static void testSimpleMethodCall() {
        System.out.println("=== 测试简单方法调用 ===");
        
        // 这些简单的方法调用应该被MethodCallLogRule记录，但不应该导致循环
        String testString = "Hello RASP";
        int length = testString.length();
        String upperCase = testString.toUpperCase();
        
        System.out.println("字符串: " + testString);
        System.out.println("长度: " + length);
        System.out.println("大写: " + upperCase);
        
        System.out.println("简单方法调用测试完成");
    }
    
    /**
     * 测试文件操作
     * 验证文件操作在开发环境下是否会导致循环日志
     */
    public static void testFileOperations() {
        System.out.println("=== 测试文件操作 ===");
        
        try {
            // 1. 测试普通文件写入
            File normalFile = new File("dev-test-normal.txt");
            FileWriter writer = new FileWriter(normalFile);
            writer.write("Development environment test\n");
            writer.write("This should be detected by FileWriteRule\n");
            writer.close();
            System.out.println("写入普通文件: " + normalFile.getName());
            
            // 2. 测试普通文件读取
            BufferedReader reader = new BufferedReader(new FileReader(normalFile));
            String line;
            System.out.println("读取普通文件内容:");
            while ((line = reader.readLine()) != null) {
                System.out.println("  " + line);
            }
            reader.close();
            
            // 3. 测试日志文件写入（应该被过滤）
            File logFile = new File("dev-test.log");
            FileWriter logWriter = new FileWriter(logFile);
            logWriter.write("This is a log file entry\n");
            logWriter.write("Should be filtered by isLogFile check\n");
            logWriter.close();
            System.out.println("写入日志文件: " + logFile.getName() + " (应该被过滤)");
            
            // 清理
            normalFile.delete();
            logFile.delete();
            
            System.out.println("文件操作测试完成");
            
        } catch (IOException e) {
            System.err.println("文件操作测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试命令执行
     * 验证命令执行在开发环境下的检测情况
     */
    public static void testCommandExecution() {
        System.out.println("=== 测试命令执行 ===");
        
        try {
            String command = System.getProperty("os.name").toLowerCase().contains("windows") ? 
                           "echo Development environment command test" : 
                           "echo 'Development environment command test'";
            
            System.out.println("执行命令: " + command);
            Process process = Runtime.getRuntime().exec(command);
            
            // 读取命令输出
            BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("  命令输出: " + line);
            }
            reader.close();
            
            int exitCode = process.waitFor();
            System.out.println("命令执行完成，退出码: " + exitCode);
            
        } catch (Exception e) {
            System.err.println("命令执行测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试反射调用
     * 验证反射操作是否会被过度记录
     */
    public static void testReflectionCalls() {
        System.out.println("=== 测试反射调用 ===");
        
        try {
            // 使用反射调用一些方法
            Class<?> stringClass = String.class;
            java.lang.reflect.Method lengthMethod = stringClass.getMethod("length");
            java.lang.reflect.Method toUpperCaseMethod = stringClass.getMethod("toUpperCase");
            
            String testString = "reflection test";
            
            // 通过反射调用方法
            Integer length = (Integer) lengthMethod.invoke(testString);
            String upperCase = (String) toUpperCaseMethod.invoke(testString);
            
            System.out.println("原字符串: " + testString);
            System.out.println("反射获取长度: " + length);
            System.out.println("反射转大写: " + upperCase);
            
            System.out.println("反射调用测试完成");
            
        } catch (Exception e) {
            System.err.println("反射调用测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试大量方法调用
     * 验证在大量方法调用的情况下是否会出现性能问题或循环日志
     */
    public static void testHighVolumeMethodCalls() {
        System.out.println("=== 测试大量方法调用 ===");
        
        try {
            // 执行一些计算密集的操作，会产生大量方法调用
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < 100; i++) {
                sb.append("Item ").append(i).append("\n");
            }
            
            String result = sb.toString();
            String[] lines = result.split("\n");
            
            System.out.println("生成了 " + lines.length + " 行文本");
            System.out.println("前3行内容:");
            for (int i = 0; i < Math.min(3, lines.length); i++) {
                System.out.println("  " + lines[i]);
            }
            
            System.out.println("大量方法调用测试完成");
            
        } catch (Exception e) {
            System.err.println("大量方法调用测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试System.out输出
     * 验证System.out.println是否会导致循环
     */
    public static void testSystemOutOutput() {
        System.out.println("=== 测试System.out输出 ===");
        
        // 这些System.out.println调用本身可能会被MethodCallLogRule记录
        // 但不应该导致无限循环
        for (int i = 0; i < 5; i++) {
            System.out.println("System.out测试输出 " + (i + 1));
        }
        
        System.err.println("System.err测试输出");
        
        System.out.println("System.out输出测试完成");
    }
    
    /**
     * 主方法 - 运行所有测试
     */
    public static void main(String[] args) {
        System.out.println("RASP开发环境配置循环日志测试开始");
        System.out.println("当前配置应该启用了MethodCallLogRule和DEBUG日志级别");
        System.out.println("========================================");
        
        // 运行各种测试
        testSimpleMethodCall();
        System.out.println();
        
        testFileOperations();
        System.out.println();
        
        testCommandExecution();
        System.out.println();
        
        testReflectionCalls();
        System.out.println();
        
        testHighVolumeMethodCalls();
        System.out.println();
        
        testSystemOutOutput();
        System.out.println();
        
        System.out.println("========================================");
        System.out.println("开发环境配置循环日志测试完成");
        System.out.println();
        System.out.println("预期结果：");
        System.out.println("1. 应该看到大量的方法调用日志（MethodCallLogRule的输出）");
        System.out.println("2. 应该看到文件操作和命令执行的检测日志");
        System.out.println("3. 不应该出现无限循环的日志输出");
        System.out.println("4. RASP内部调用应该被过滤，不会被MethodCallLogRule记录");
        System.out.println("5. 日志文件操作应该被FileWriteRule过滤");
        
        // 等待观察
        try {
            System.out.println("\n等待5秒钟，观察是否有持续的循环日志...");
            Thread.sleep(5000);
            System.out.println("观察完成！如果没有看到持续的重复日志，说明循环问题已解决。");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}

package com.rasp.rules.command;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.CommandExecutionEvent;
import com.rasp.api.event.HttpRequestEvent;
import com.rasp.api.rule.AbstractRule;
import com.rasp.api.rule.RuleResult;
import com.rasp.api.context.RequestContext;

import java.util.Arrays;
import java.util.List;

/**
 * 命令执行规则
 * 检测和分析系统命令执行，识别潜在的安全威胁
 */
public class CommandExecutionRule extends AbstractRule {
    
    // 危险命令列表
    private static final List<String> DANGEROUS_COMMANDS = Arrays.asList(
        "rm", "del", "format", "fdisk", "mkfs",
        "wget", "curl", "nc", "netcat", "telnet",
        "powershell", "cmd", "bash", "sh",
        "whoami", "id", "ps", "netstat", "ifconfig",
        "cat", "type", "more", "less", "head", "tail"
    );
    
    // 敏感路径
    private static final List<String> SENSITIVE_PATHS = Arrays.asList(
        "/etc/passwd", "/etc/shadow", "/etc/hosts",
        "C:\\Windows\\System32", "C:\\Users",
        "/home", "/root", "/var/log"
    );
    
    @Override
    public String getName() {
        return "CommandExecutionRule";
    }
    
    @Override
    public String getDescription() {
        return "Detect and analyze command execution for security threats";
    }
    
    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{HookEvent.EventType.COMMAND_EXECUTION};
    }

    @Override
    protected RuleResult doProcess(HookEvent event) {
        CommandExecutionEvent cmdEvent = (CommandExecutionEvent) event;
        String command = cmdEvent.getCommand();

        // 检查是否为RASP内部操作
        if (isInternalOperation(cmdEvent)) {
            return RuleResult.allow(getName());
        }

        // 打印所有命令执行信息，确保规则被处理
        logger.debug("[CommandExecutionRule] RULE PROCESSING STARTED");
        logger.debug("Event Type: {}", event.getEventType());
        logger.debug("Class: {}", cmdEvent.getClassName());
        logger.debug("Method: {}", cmdEvent.getMethodName());
        logger.debug("Command: {}", command != null ? command : "NULL");
        logger.debug("Timestamp: {}", new java.util.Date());

        if (command == null || command.trim().isEmpty()) {
            logger.debug("[CommandExecutionRule] Empty or null command detected");
            return RuleResult.allow(getName());
        }

        logger.debug("[CommandExecutionRule] Analyzing command execution: {}", command);

        // 检查是否有HTTP请求上下文
        HttpRequestEvent httpRequest = RequestContext.getHttpRequestEvent();
        if (httpRequest != null) {
            logger.debug("[CommandExecutionRule] HTTP Request Context Found!");
            printHttpRequestInfo(httpRequest);

            // 检查命令是否来自HTTP请求参数
            boolean isFromHttpRequest = checkCommandFromHttpRequest(command, httpRequest);
            if (isFromHttpRequest) {
                logger.info("[CommandExecutionRule] POTENTIAL COMMAND INJECTION DETECTED!");
                logger.info("Command '{}' appears to come from HTTP request parameters", command);
                return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH,
                    "Potential command injection: '" + command + "' from HTTP request");
            }
        } else {
            logger.info("[CommandExecutionRule] No HTTP request context found - command executed outside web request");
        }

        // 简化逻辑：所有命令都记录，不进行复杂检查
        logger.debug("[CommandExecutionRule] Command execution logged successfully");
        logger.debug("[CommandExecutionRule] RULE PROCESSING COMPLETED");

        // 返回日志结果，确保有输出
        return RuleResult.log(getName(), RuleResult.RiskLevel.LOW,
                            "Command execution monitored and logged: " + command);
    }

    /**
     * 检查是否为RASP内部操作
     * 避免RASP自身操作触发规则导致循环
     * 注意：这里只过滤真正的RASP内部操作，不过滤正常的应用调用
     */
    private boolean isInternalOperation(CommandExecutionEvent event) {
        // 检查调用栈中是否包含RASP内部调用
        StackTraceElement[] stackTrace = event.getStackTrace();
        if (stackTrace != null) {
            // 检查调用栈的前几层，看是否主要是RASP内部调用
            int raspInternalCount = 0;
            int totalFrames = Math.min(10, stackTrace.length); // 只检查前10层

            for (int i = 0; i < totalFrames; i++) {
                String stackClassName = stackTrace[i].getClassName();

                // RASP内部调用
                if (stackClassName.startsWith("com.rasp.")) {
                    raspInternalCount++;
                }

                // 如果是RASP的Spy回调，这通常是RASP内部操作
                if (stackClassName.contains("SpyCallbackImpl") ||
                    stackClassName.contains("SpyCallback")) {
                    raspInternalCount += 2; // 给更高权重
                }
            }

            // 如果调用栈中RASP内部调用占主导地位，认为是内部操作
            // 这样可以过滤掉RASP自身的测试或内部命令执行，但不会过滤正常的攻击
            if (raspInternalCount > totalFrames / 2) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查危险命令
     */
    private RuleResult checkDangerousCommands(String command) {
        String lowerCommand = command.toLowerCase();
        
        for (String dangerousCmd : DANGEROUS_COMMANDS) {
            if (lowerCommand.contains(dangerousCmd)) {
                String message = String.format("Dangerous command detected: %s in '%s'", dangerousCmd, command);
                logger.info("{}", message);
                
                // 对于一些命令，只是告警而不阻断
                if (isInformationGatheringCommand(dangerousCmd)) {
                    return RuleResult.alert(getName(), RuleResult.RiskLevel.MEDIUM, message);
                } else {
                    return RuleResult.block(getName(), RuleResult.RiskLevel.HIGH, message);
                }
            }
        }
        
        return RuleResult.allow(getName());
    }
    
    /**
     * 检查敏感路径
     */
    private RuleResult checkSensitivePaths(String command) {
        for (String sensitivePath : SENSITIVE_PATHS) {
            if (command.contains(sensitivePath)) {
                String message = String.format("Sensitive path access: %s in '%s'", sensitivePath, command);
                logger.warn("{}", message);
                return RuleResult.alert(getName(), RuleResult.RiskLevel.MEDIUM, message);
            }
        }

        return RuleResult.allow(getName());
    }
    
    /**
     * 检查命令注入模式
     */
    private RuleResult checkCommandInjection(String command) {
        // 检查常见的命令注入字符
        String[] injectionPatterns = {
            ";", "&&", "||", "|", "`", "$(",
            "$(", "${", "<!--", "-->", "<script", "</script>"
        };
        
        for (String pattern : injectionPatterns) {
            if (command.contains(pattern)) {
                String message = String.format("Command injection pattern detected: %s in '%s'", pattern, command);
                logger.error("{}", message);
                return RuleResult.block(getName(), RuleResult.RiskLevel.CRITICAL, message);
            }
        }

        return RuleResult.allow(getName());
    }
    
    /**
     * 判断是否是信息收集命令（通常只需要警告而不阻断）
     */
    private boolean isInformationGatheringCommand(String command) {
        return Arrays.asList("whoami", "id", "ps", "netstat", "ifconfig").contains(command);
    }

    /**
     * 打印HTTP请求信息
     */
    private void printHttpRequestInfo(HttpRequestEvent httpRequest) {
        logger.debug("=== HTTP Request Information ===");
        logger.debug("Request URL: " + httpRequest.getRequestUrl());
        logger.debug("Request Method: " + httpRequest.getRequestMethod());
        logger.debug("Request Path: " + httpRequest.getRequestPath());
        logger.debug("Query String: " + httpRequest.getQueryString());
        logger.debug("Client IP: " + httpRequest.getClientIp());
        logger.debug("User-Agent: " + httpRequest.getUserAgent());
        logger.debug("Session ID: " + httpRequest.getSessionId());

        // 打印请求参数
        if (httpRequest.getRequestParameters() != null && !httpRequest.getRequestParameters().isEmpty()) {
            logger.debug("Request Parameters:");
            for (java.util.Map.Entry<String, String[]> entry : httpRequest.getRequestParameters().entrySet()) {
                String paramName = entry.getKey();
                String[] paramValues = entry.getValue();
                if (paramValues != null) {
                    for (String value : paramValues) {
                        logger.debug("    " + paramName + " = " + value);
                    }
                }
            }
        }

        // 打印请求头
        if (httpRequest.getRequestHeaders() != null && !httpRequest.getRequestHeaders().isEmpty()) {
            logger.debug("Request Headers:");
            for (java.util.Map.Entry<String, String> entry : httpRequest.getRequestHeaders().entrySet()) {
                logger.debug("    " + entry.getKey() + ": " + entry.getValue());
            }
        }

        // 打印请求体
        if (httpRequest.getRequestBody() != null && !httpRequest.getRequestBody().isEmpty()) {
            logger.debug("Request Body: " + httpRequest.getRequestBody());
        }

        logger.debug("=== End of HTTP Request Info ===");
    }

    /**
     * 检查命令是否来自HTTP请求参数
     */
    private boolean checkCommandFromHttpRequest(String command, HttpRequestEvent httpRequest) {
        if (command == null || httpRequest == null) {
            return false;
        }

        // 检查查询字符串
        if (httpRequest.getQueryString() != null && httpRequest.getQueryString().contains(command)) {
            logger.debug("Command '{}' found in query string: {}", command, httpRequest.getQueryString());
            return true;
        }

        // 检查请求参数
        if (httpRequest.getRequestParameters() != null) {
            for (java.util.Map.Entry<String, String[]> entry : httpRequest.getRequestParameters().entrySet()) {
                String paramName = entry.getKey();
                String[] paramValues = entry.getValue();
                if (paramValues != null) {
                    for (String value : paramValues) {
                        if (value != null && value.contains(command)) {
                            logger.debug("Command '{}' found in parameter '{}': {}", command, paramName, value);
                            return true;
                        }
                    }
                }
            }
        }

        // 检查请求体
        if (httpRequest.getRequestBody() != null && httpRequest.getRequestBody().contains(command)) {
            logger.debug("Command '{}' found in request body: {}", command, httpRequest.getRequestBody());
            return true;
        }

        return false;
    }

}

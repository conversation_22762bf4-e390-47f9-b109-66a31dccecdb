package com.rasp.hooks.deserialization;

import com.rasp.api.event.DeserializationEvent;
import com.rasp.api.event.HookEvent;
import com.rasp.api.hook.AbstractHook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 反序列化Hook
 * 直接监控反序列化方法的调用，用于增强检测能力
 */
public class DeserializationHook extends AbstractHook {
    
    private static final Logger logger = LoggerFactory.getLogger(DeserializationHook.class);
    
    @Override
    public String getName() {
        return "DeserializationHook";
    }
    
    @Override
    public String getDescription() {
        return "Hook for monitoring deserialization method calls";
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public String[] getClassNamePatterns() {
        return new String[]{
            // Java原生反序列化
            "java.io.ObjectInputStream",
            
            // Fastjson
            "com.alibaba.fastjson.JSON",
            "com.alibaba.fastjson2.JSON",
            
            // <PERSON>
            "com.fasterxml.jackson.databind.ObjectMapper",
            
            // XStream
            "com.thoughtworks.xstream.XStream",
            
            // Kryo
            "com.esotericsoftware.kryo.Kryo",
            
            // Hessian
            "com.caucho.hessian.io.HessianInput",
            
            // Apache Commons Collections (危险的Transformer)
            "org.apache.commons.collections.functors.InvokerTransformer",
            "org.apache.commons.collections.functors.InstantiateTransformer",
            "org.apache.commons.collections4.functors.InvokerTransformer",
            
            // Apache Commons BeanUtils
            "org.apache.commons.beanutils.PropertyUtils",
            "org.apache.commons.beanutils.BeanUtils"
        };
    }
    
    @Override
    public String[] getMethodNamePatterns() {
        return new String[]{
            // Java原生反序列化
            "readObject",
            "readUnshared",
            "defaultReadObject",
            
            // JSON反序列化
            "parseObject",
            "parse",
            "readValue",
            "readTree",
            
            // XML反序列化
            "fromXML",
            
            // 其他反序列化
            "readClassAndObject",
            
            // 危险的反射调用
            "transform",
            "getProperty",
            "populate"
        };
    }
    
    @Override
    public String[] getMethodSignaturePatterns() {
        return null; // 不限制方法签名
    }
    
    @Override
    public HookEvent onMethodEnter(String className, String methodName, String methodSignature,
                                  Object target, Object[] arguments) {
        
        logger.debug("[DeserializationHook] Monitoring deserialization call: {}.{}", className, methodName);
        
        DeserializationEvent event = new DeserializationEvent();
        event.setClassName(className);
        event.setMethodName(methodName);
        event.setMethodSignature(methodSignature);
        event.setArguments(arguments);
        
        // 分析反序列化类型和风险等级
        analyzeDeserializationRisk(event, className, methodName, arguments);
        
        logger.debug("[DeserializationHook] Created deserialization event: {} (risk: {})", 
                    event.getAttackType(), event.getRiskLevel());
        
        return event;
    }
    
    /**
     * 分析反序列化的风险等级和类型
     */
    private void analyzeDeserializationRisk(DeserializationEvent event, String className, 
                                          String methodName, Object[] arguments) {
        
        String fullMethodName = className + "." + methodName;
        
        // 设置攻击类型和风险等级
        if (className.contains("ObjectInputStream")) {
            event.setAttackType("Java Native Deserialization");
            event.setRiskLevel(DeserializationEvent.RiskLevel.HIGH);
            event.setAttackDescription("Java native deserialization detected, potential gadget chain execution");
            
        } else if (className.contains("fastjson")) {
            event.setAttackType("Fastjson Deserialization");
            event.setRiskLevel(DeserializationEvent.RiskLevel.HIGH);
            event.setAttackDescription("Fastjson deserialization detected, potential autoType bypass");
            
        } else if (className.contains("jackson")) {
            event.setAttackType("Jackson Deserialization");
            event.setRiskLevel(DeserializationEvent.RiskLevel.MEDIUM);
            event.setAttackDescription("Jackson deserialization detected, check for polymorphic type handling");
            
        } else if (className.contains("XStream")) {
            event.setAttackType("XStream Deserialization");
            event.setRiskLevel(DeserializationEvent.RiskLevel.HIGH);
            event.setAttackDescription("XStream XML deserialization detected, potential remote code execution");
            
        } else if (className.contains("InvokerTransformer")) {
            event.setAttackType("Commons Collections Gadget");
            event.setRiskLevel(DeserializationEvent.RiskLevel.CRITICAL);
            event.setAttackDescription("Apache Commons Collections InvokerTransformer detected, likely gadget chain");
            
        } else if (className.contains("InstantiateTransformer")) {
            event.setAttackType("Commons Collections Gadget");
            event.setRiskLevel(DeserializationEvent.RiskLevel.CRITICAL);
            event.setAttackDescription("Apache Commons Collections InstantiateTransformer detected, likely gadget chain");
            
        } else if (className.contains("PropertyUtils") || className.contains("BeanUtils")) {
            event.setAttackType("BeanUtils Property Access");
            event.setRiskLevel(DeserializationEvent.RiskLevel.HIGH);
            event.setAttackDescription("Apache Commons BeanUtils property access detected, potential property injection");
            
        } else {
            event.setAttackType("Generic Deserialization");
            event.setRiskLevel(DeserializationEvent.RiskLevel.MEDIUM);
            event.setAttackDescription("Generic deserialization method detected: " + fullMethodName);
        }
        
        // 设置匹配的黑名单方法
        event.setMatchedBlacklistMethod(fullMethodName);
        
        // 分析参数以获取更多上下文信息
        analyzeDeserializationArguments(event, arguments);
    }
    
    /**
     * 分析反序列化参数
     */
    private void analyzeDeserializationArguments(DeserializationEvent event, Object[] arguments) {
        if (arguments == null || arguments.length == 0) {
            return;
        }
        
        try {
            // 对于JSON反序列化，检查输入字符串
            if (event.getAttackType().contains("json") || event.getAttackType().contains("Jackson")) {
                for (Object arg : arguments) {
                    if (arg instanceof String) {
                        String jsonString = (String) arg;
                        if (containsSuspiciousJsonContent(jsonString)) {
                            event.setRiskLevel(DeserializationEvent.RiskLevel.HIGH);
                            event.setAttackDescription(event.getAttackDescription() + " - Suspicious JSON content detected");
                        }
                        break;
                    }
                }
            }
            
            // 对于原生Java反序列化，检查输入流
            if (event.getAttackType().contains("Java Native")) {
                // 可以在这里添加对输入流的分析
                logger.debug("Java native deserialization with {} arguments", arguments.length);
            }
            
        } catch (Exception e) {
            logger.debug("Error analyzing deserialization arguments: {}", e.getMessage());
        }
    }
    
    /**
     * 检查JSON内容是否包含可疑的类型信息
     */
    private boolean containsSuspiciousJsonContent(String jsonString) {
        if (jsonString == null || jsonString.length() > 10000) { // 避免分析过大的字符串
            return false;
        }
        
        // 检查常见的危险类型标识
        String[] suspiciousPatterns = {
            "@type",
            "com.sun.rowset.JdbcRowSetImpl",
            "org.apache.commons.collections",
            "org.springframework.beans.factory",
            "javax.management.BadAttributeValueExpException",
            "java.rmi.server.UnicastRemoteObject",
            "java.util.PriorityQueue"
        };
        
        String lowerJson = jsonString.toLowerCase();
        for (String pattern : suspiciousPatterns) {
            if (lowerJson.contains(pattern.toLowerCase())) {
                logger.debug("Suspicious JSON pattern detected: {}", pattern);
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public void onMethodReturn(HookEvent event, Object returnValue) {
        super.onMethodReturn(event, returnValue);
        
        if (event instanceof DeserializationEvent) {
            DeserializationEvent deserEvent = (DeserializationEvent) event;
            logger.debug("Deserialization completed: {} -> {}", 
                        deserEvent.getAttackType(), returnValue != null ? returnValue.getClass().getSimpleName() : "null");
        }
    }
    
    @Override
    public void onMethodThrow(HookEvent event, Throwable throwable) {
        super.onMethodThrow(event, throwable);
        
        if (event instanceof DeserializationEvent) {
            DeserializationEvent deserEvent = (DeserializationEvent) event;
            logger.warn("Deserialization failed: {} -> {}", 
                       deserEvent.getAttackType(), throwable.getMessage());
        }
    }
}

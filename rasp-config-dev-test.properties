# RASP开发环境测试配置文件
# 专门用于测试循环日志问题的修复效果

# ============================================================================
# Global Configuration
# ============================================================================

rasp.enabled=true
rasp.log.level=DEBUG

# ============================================================================
# Hook Configuration
# ============================================================================

# HTTP Request Hook - ENABLED for testing
rasp.hook.HttpRequestHook.enabled=true

# Command Execution Hook - ENABLED for security testing
rasp.hook.CommandExecutionHook.enabled=true

# File Read Hook - ENABLED for file access monitoring
rasp.hook.FileReadHook.enabled=true

# File Write Hook - ENABLED for file write monitoring
rasp.hook.FileWriteHook.enabled=true

# ============================================================================
# Rule Configuration
# ============================================================================

# HTTP Request Logging Rule - ENABLED for request monitoring
rasp.rule.HttpRequestLogRule.enabled=true

# Command Execution Rule - ENABLED for command monitoring
rasp.rule.CommandExecutionRule.enabled=true

# File Read Rule - ENABLED for file read monitoring
rasp.rule.FileReadRule.enabled=true

# File Write Rule - ENABLED for file write monitoring
rasp.rule.FileWriteRule.enabled=true

# Method Call Logging Rule - ENABLED for detailed debugging (this is the problematic one)
rasp.rule.MethodCallLogRule.enabled=true

# Deserialization Attack Detection Rule - ENABLED for security testing
rasp.rule.DeserializationStackTraceRule.enabled=true

# ============================================================================
# Security Configuration
# ============================================================================

rasp.security.default_action=LOG
rasp.security.alerts.enabled=true
rasp.security.alert_threshold=3

# ============================================================================
# Deserialization Attack Detection Configuration - Test Mode
# ============================================================================

# Enable deserialization attack detection
deserialization.rule.enabled=true

# Disable blocking mode for testing (only log)
deserialization.blocking.enabled=false

# Lower minimum risk level for testing (detect more potential issues)
deserialization.min.block.risk.level=MEDIUM

# Enable detailed stack trace logging for debugging
deserialization.log.detailed.stack=true

# Standard stack analysis depth for testing
deserialization.stack.analysis.max.depth=50

# Enable security alerts for deserialization attacks
deserialization.log.security.alerts=true

# Debug log level for testing
deserialization.log.level=DEBUG

# Enable performance monitoring for testing
deserialization.stats.enabled=true
deserialization.stats.log.interval=30000

# ============================================================================
# Performance Configuration
# ============================================================================

rasp.performance.max_hook_time=1000
rasp.performance.max_rule_time=500
rasp.performance.enable_metrics=true

# ============================================================================
# Debug Configuration
# ============================================================================

rasp.debug.enabled=true
rasp.debug.print_stack_trace=false
rasp.debug.max_stack_depth=20

# ============================================================================
# Test-specific Configuration
# ============================================================================

# 这些配置专门用于测试循环日志修复效果
test.cyclic.logging.detection=true
test.method.call.logging.verbose=true
test.file.operation.monitoring=true
test.command.execution.monitoring=true

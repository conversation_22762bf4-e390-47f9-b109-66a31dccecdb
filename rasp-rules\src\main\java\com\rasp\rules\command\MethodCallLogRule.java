package com.rasp.rules.command;

import com.rasp.api.event.HookEvent;
import com.rasp.api.rule.AbstractRule;
import com.rasp.api.rule.RuleResult;

/**
 * 方法调用日志规则
 * 示例规则，用于打印方法调用的详细信息
 */
public class MethodCallLogRule extends AbstractRule {

    private volatile boolean enabled = false;
    
    @Override
    public String getName() {
        return "MethodCallLogRule";
    }
    
    @Override
    public String getDescription() {
        return "Log method call information for monitoring and analysis";
    }
    
    @Override
    public int getPriority() {
        return 50; // 中等优先级
    }
    
    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{
            HookEvent.EventType.METHOD_CALL,
            HookEvent.EventType.FILE_ACCESS,
            HookEvent.EventType.SQL_EXECUTION,
            HookEvent.EventType.COMMAND_EXECUTION
        };
    }

    /**
     * 检查是否为内部调用
     * 过滤RASP内部调用和可能导致循环的调用
     */
    private boolean isInternalCall(HookEvent event) {
        String className = event.getClassName();

        if (className == null) {
            return false;
        }

        // 过滤RASP内部类
        if (className.startsWith("com.rasp.")) {
            return true;
        }

        // 过滤日志框架类
        if (className.startsWith("ch.qos.logback.") ||
            className.startsWith("org.slf4j.") ||
            className.startsWith("java.util.logging.") ||
            className.startsWith("org.apache.logging.") ||
            className.startsWith("org.apache.log4j.")) {
            return true;
        }

        // 过滤System.out相关的类（避免循环）
        if (className.startsWith("java.io.PrintStream") ||
            className.startsWith("java.io.BufferedWriter") ||
            className.startsWith("java.io.OutputStreamWriter") ||
            className.startsWith("java.io.FileOutputStream") ||
            className.startsWith("java.io.FileWriter") ||
            className.startsWith("java.io.Writer") ||
            className.startsWith("java.io.OutputStream")) {
            return true;
        }

        // 过滤JVM内部类
        if (className.startsWith("sun.") ||
            className.startsWith("com.sun.") ||
            className.startsWith("jdk.internal.") ||
            className.startsWith("java.lang.reflect.")) {
            return true;
        }

        // 过滤Spring Boot Loader
        if (className.startsWith("org.springframework.boot.loader.")) {
            return true;
        }

        // 检查调用栈，如果调用栈中包含大量RASP内部调用，可能是内部操作
        StackTraceElement[] stackTrace = event.getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            int raspInternalCount = 0;
            int totalFrames = Math.min(10, stackTrace.length);

            for (int i = 0; i < totalFrames; i++) {
                String stackClassName = stackTrace[i].getClassName();
                if (stackClassName.startsWith("com.rasp.") ||
                    stackClassName.startsWith("ch.qos.logback.") ||
                    stackClassName.startsWith("org.slf4j.")) {
                    raspInternalCount++;
                }
            }

            // 如果调用栈中有超过一半是RASP内部或日志框架调用，过滤掉
            if (raspInternalCount > totalFrames / 2) {
                return true;
            }
        }

        return false;
    }

    @Override
    protected RuleResult doProcess(HookEvent event) {
        // 过滤RASP内部调用和日志相关调用
        if (isInternalCall(event)) {
            return RuleResult.allow(getName());
        }

        // 打印方法调用的详细信息
        System.out.println("=== Method Call Information ===");
        System.out.println("Event Type: " + event.getEventType());
        System.out.println("Timestamp: " + event.getTimestamp());
        System.out.println("Thread: " + event.getThreadName() + " (ID: " + event.getThreadId() + ")");
        
        // 方法信息
        System.out.println("--- Method Info ---");
        System.out.println("Class: " + event.getClassName());
        System.out.println("Method: " + event.getMethodName());
        System.out.println("Signature: " + event.getMethodSignature());
        
        // 参数信息
        System.out.println("--- Arguments ---");
        Object[] arguments = event.getArguments();
        if (arguments != null && arguments.length > 0) {
            System.out.println("Arguments: " + formatArguments(arguments));
            
            // 详细显示每个参数
            for (int i = 0; i < arguments.length; i++) {
                Object arg = arguments[i];
                if (arg != null) {
                    System.out.println("  [" + i + "] " + arg.getClass().getSimpleName() + ": " + 
                                     formatArgumentValue(arg));
                } else {
                    System.out.println("  [" + i + "] null");
                }
            }
        } else {
            System.out.println("No arguments");
        }
        
        // 返回值信息
        System.out.println("--- Return Value ---");
        Object returnValue = event.getReturnValue();
        if (returnValue != null) {
            System.out.println("Return Type: " + returnValue.getClass().getSimpleName());
            System.out.println("Return Value: " + formatArgumentValue(returnValue));
        } else {
            System.out.println("No return value (void or not set)");
        }
        
        // 异常信息
        System.out.println("--- Exception ---");
        Throwable throwable = event.getThrowable();
        if (throwable != null) {
            System.out.println("Exception Type: " + throwable.getClass().getSimpleName());
            System.out.println("Exception Message: " + throwable.getMessage());
        } else {
            System.out.println("No exception");
        }
        
        // 堆栈信息（只显示前几层）
        System.out.println("--- Stack Trace (Top 3) ---");
        StackTraceElement[] stackTrace = event.getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            int maxLines = Math.min(3, stackTrace.length);
            for (int i = 0; i < maxLines; i++) {
                System.out.println("\tat " + stackTrace[i]);
            }
            if (stackTrace.length > maxLines) {
                System.out.println("\t... and " + (stackTrace.length - maxLines) + " more");
            }
        }
        
        // 扩展属性
        System.out.println("--- Attributes ---");
        if (event.getAttributes() != null && !event.getAttributes().isEmpty()) {
            for (java.util.Map.Entry<String, Object> entry : event.getAttributes().entrySet()) {
                System.out.println(entry.getKey() + ": " + entry.getValue());
            }
        } else {
            System.out.println("No attributes");
        }
        
        System.out.println("=== End of Method Call Info ===\n");
        
        // 记录日志并允许继续执行
        return RuleResult.log(getName(), RuleResult.RiskLevel.LOW, 
                             "Method call logged: " + event.getClassName() + "." + event.getMethodName());
    }
    
    /**
     * 格式化参数值，避免过长的输出
     * @param value 参数值
     * @return 格式化后的字符串
     */
    private String formatArgumentValue(Object value) {
        if (value == null) {
            return "null";
        }
        
        String str = value.toString();
        
        // 限制字符串长度，避免输出过长
        if (str.length() > 200) {
            return str.substring(0, 200) + "... (truncated)";
        }
        
        return str;
    }
}

# FileReadRule 用户可控性检测逻辑

## 🎯 改进目标

解决任意文件读取防护中的误报问题，通过用户可控性判断来精确区分：
- **真正的攻击行为**：文件路径来自HTTP请求（用户可控）
- **内部操作行为**：应用内部的正常文件读取操作

## 🔧 核心改进逻辑

### 原有逻辑问题
```
敏感文件访问 → 直接告警/阻断 → 大量误报
```

### 新的智能逻辑
```
1. 检查文件路径是否来自HTTP请求（用户可控性判断）
2. 根据用户可控性调整响应级别：
   - 用户可控 + 敏感文件 → 真正攻击 → 告警/阻断
   - 非用户可控 + 敏感文件 → 内部操作 → 仅LOG记录
```

## 📋 实现细节

### 1. 用户可控性检查
```java
/**
 * 检查文件路径是否来自HTTP请求（用户可控性检查）
 */
private boolean checkIfPathFromHttpRequest(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
    if (httpRequest == null) {
        return false; // 无HTTP上下文，不是用户可控
    }
    
    // 检查所有路径变体是否来自HTTP请求
    boolean pathFromRequest = checkFilePathFromHttpRequest(filePath, httpRequest) ||
                             checkFilePathFromHttpRequest(absolutePath, httpRequest) ||
                             checkFilePathFromHttpRequest(canonicalPath, httpRequest);
    
    return pathFromRequest;
}
```

### 2. 分级响应策略

#### 🔴 敏感文件访问
```java
if (isUserControllable) {
    // 用户可控的敏感文件访问 - 真正的攻击
    logger.error("[CRITICAL SECURITY ALERT] User-controllable sensitive file access");
    return RuleResult.block(getName(), RuleResult.RiskLevel.CRITICAL, message);
} else {
    // 非用户可控的敏感文件访问 - 可能是内部操作，降级为LOG
    logger.info("[INTERNAL OPERATION] Sensitive file access (not user-controllable)");
    return RuleResult.log(getName(), RuleResult.RiskLevel.MEDIUM, message);
}
```

#### 🟠 敏感目录访问
```java
if (isUserControllable) {
    // 用户可控 → 阻断
    return RuleResult.block(getName(), RuleResult.RiskLevel.HIGH, message);
} else {
    // 非用户可控 → 仅LOG
    return RuleResult.log(getName(), RuleResult.RiskLevel.LOW, message);
}
```

#### 🟡 敏感扩展名
```java
// 密钥文件
if (Arrays.asList("key", "pem", "crt", "cer", "p12", "jks", "keystore").contains(extension)) {
    if (isUserControllable) {
        return RuleResult.block(getName(), RuleResult.RiskLevel.HIGH, message);
    } else {
        return RuleResult.log(getName(), RuleResult.RiskLevel.MEDIUM, message);
    }
}

// 配置文件
if (Arrays.asList("conf", "config", "cfg", "ini", "properties", "yml", "yaml", "env").contains(extension)) {
    if (isUserControllable) {
        return RuleResult.alert(getName(), RuleResult.RiskLevel.MEDIUM, message);
    } else {
        return RuleResult.log(getName(), RuleResult.RiskLevel.LOW, message);
    }
}
```

## 📊 响应级别对比

### 用户可控的文件访问（真正攻击）
| 文件类型 | 原响应级别 | 新响应级别 | 动作 |
|---------|-----------|-----------|------|
| 敏感文件 | CRITICAL/BLOCK | CRITICAL/BLOCK | 阻断 |
| 敏感目录 | HIGH/BLOCK | HIGH/BLOCK | 阻断 |
| 密钥文件 | HIGH/BLOCK | HIGH/BLOCK | 阻断 |
| 配置文件 | MEDIUM/ALERT | MEDIUM/ALERT | 告警 |
| 敏感关键词 | MEDIUM/ALERT | MEDIUM/ALERT | 告警 |

### 非用户可控的文件访问（内部操作）
| 文件类型 | 原响应级别 | 新响应级别 | 动作 |
|---------|-----------|-----------|------|
| 敏感文件 | CRITICAL/BLOCK | MEDIUM/LOG | 仅记录 |
| 敏感目录 | HIGH/BLOCK | LOW/LOG | 仅记录 |
| 密钥文件 | HIGH/BLOCK | MEDIUM/LOG | 仅记录 |
| 配置文件 | MEDIUM/ALERT | LOW/LOG | 仅记录 |
| 敏感关键词 | MEDIUM/ALERT | LOW/LOG | 仅记录 |

## 🎯 攻击场景示例

### 场景1: 真正的攻击（用户可控）
```
HTTP请求: GET /download?file=/etc/passwd
文件读取: /etc/passwd
判断结果: 用户可控 = true
响应: CRITICAL/BLOCK - 阻断攻击
```

### 场景2: 内部操作（非用户可控）
```
HTTP请求: GET /login
文件读取: /etc/passwd (应用内部验证)
判断结果: 用户可控 = false
响应: MEDIUM/LOG - 仅记录，不告警
```

### 场景3: 配置文件读取
```
HTTP请求: GET /config?file=application.properties
文件读取: application.properties
判断结果: 用户可控 = true
响应: MEDIUM/ALERT - 告警但不阻断
```

## 💡 优势特性

### 1. **精确检测**
- 只对真正的用户可控攻击进行告警/阻断
- 大幅减少内部操作的误报

### 2. **智能分级**
- 根据用户可控性动态调整响应级别
- 保持对真正攻击的高敏感度

### 3. **详细日志**
```java
// 用户可控攻击
logger.error("[CRITICAL SECURITY ALERT] User-controllable sensitive file access");

// 内部操作
logger.info("[INTERNAL OPERATION] Sensitive file access (not user-controllable)");
```

### 4. **路径变体检测**
- 检查原始路径、绝对路径、规范路径
- 支持URL编码、路径分隔符标准化
- 文件名、目录名分别匹配

## 🔍 检测机制

### HTTP请求参数检测
```java
// 查询字符串检测
checkPathInString(filePath, normalizedPath, httpRequest.getQueryString(), "query string")

// 请求参数检测
for (Map.Entry<String, String[]> entry : httpRequest.getRequestParameters().entrySet()) {
    checkPathInString(filePath, normalizedPath, value, "parameter '" + paramName + "'")
}

// 请求体检测
checkPathInString(filePath, normalizedPath, httpRequest.getRequestBody(), "request body")
```

### 路径匹配策略
1. **精确匹配**: 完全相等
2. **包含匹配**: 路径包含在请求中
3. **标准化匹配**: URL解码后匹配
4. **文件名匹配**: 提取文件名单独匹配

## 🚀 部署效果

### 误报减少
- **内部操作**: 从告警/阻断降级为LOG记录
- **真正攻击**: 保持原有的检测和阻断能力

### 日志优化
- **ERROR级别**: 仅用户可控的高风险攻击
- **WARN级别**: 仅用户可控的中等风险攻击
- **INFO级别**: 内部操作记录
- **DEBUG级别**: 详细分析信息

### 性能提升
- 减少不必要的告警处理
- 降低安全运营成本
- 提高真实攻击的识别准确度

## 📈 监控指标

### 关键指标
- **用户可控文件访问次数**: 真正的攻击尝试
- **内部文件操作次数**: 应用正常行为
- **误报率**: 显著降低
- **检测准确率**: 显著提升

这个改进让FileReadRule具备了更智能的检测能力，能够精确区分攻击行为和内部操作，大幅提升了防护效果！

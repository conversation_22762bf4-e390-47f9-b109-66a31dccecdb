package examples;

import java.io.*;

/**
 * 简化的循环测试示例
 * 验证简单的文件路径过滤方法是否有效
 */
public class SimpleCyclicTestExample {
    
    /**
     * 测试日志文件写入
     * 这些操作应该被过滤，不会触发规则
     */
    public static void testLogFileOperations() {
        System.out.println("=== 测试日志文件操作 ===");
        
        try {
            // 创建一个.log文件
            File logFile = new File("test-rasp.log");
            FileWriter writer = new FileWriter(logFile);
            writer.write("This is a test log entry\n");
            writer.write("Timestamp: " + System.currentTimeMillis() + "\n");
            writer.close();
            
            System.out.println("写入日志文件: " + logFile.getName());
            
            // 读取日志文件
            BufferedReader reader = new BufferedReader(new FileReader(logFile));
            String line;
            System.out.println("读取日志文件内容:");
            while ((line = reader.readLine()) != null) {
                System.out.println("  " + line);
            }
            reader.close();
            
            // 清理
            logFile.delete();
            System.out.println("日志文件操作完成，应该没有触发规则");
            
        } catch (IOException e) {
            System.err.println("日志文件操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试logs目录中的文件操作
     */
    public static void testLogsDirectoryOperations() {
        System.out.println("=== 测试logs目录操作 ===");
        
        try {
            // 创建logs目录
            File logsDir = new File("logs");
            if (!logsDir.exists()) {
                logsDir.mkdir();
            }
            
            // 在logs目录中创建文件
            File logFile = new File(logsDir, "application.log");
            FileWriter writer = new FileWriter(logFile);
            writer.write("Application log entry\n");
            writer.write("Level: INFO\n");
            writer.write("Message: Test message\n");
            writer.close();
            
            System.out.println("写入logs目录文件: " + logFile.getPath());
            
            // 读取文件
            BufferedReader reader = new BufferedReader(new FileReader(logFile));
            String line;
            System.out.println("读取logs目录文件内容:");
            while ((line = reader.readLine()) != null) {
                System.out.println("  " + line);
            }
            reader.close();
            
            // 清理
            logFile.delete();
            logsDir.delete();
            System.out.println("logs目录操作完成，应该没有触发规则");
            
        } catch (IOException e) {
            System.err.println("logs目录操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试普通文件操作
     * 这些操作应该被检测到
     */
    public static void testNormalFileOperations() {
        System.out.println("=== 测试普通文件操作 ===");
        
        try {
            // 创建普通文件
            File normalFile = new File("normal-test-file.txt");
            FileWriter writer = new FileWriter(normalFile);
            writer.write("This is a normal file\n");
            writer.write("It should be detected by RASP rules\n");
            writer.close();
            
            System.out.println("写入普通文件: " + normalFile.getName());
            
            // 读取普通文件
            BufferedReader reader = new BufferedReader(new FileReader(normalFile));
            String line;
            System.out.println("读取普通文件内容:");
            while ((line = reader.readLine()) != null) {
                System.out.println("  " + line);
            }
            reader.close();
            
            // 清理
            normalFile.delete();
            System.out.println("普通文件操作完成，应该被RASP规则检测到");
            
        } catch (IOException e) {
            System.err.println("普通文件操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试命令执行
     * 验证命令执行检测是否正常工作
     */
    public static void testCommandExecution() {
        System.out.println("=== 测试命令执行 ===");
        
        try {
            String command = System.getProperty("os.name").toLowerCase().contains("windows") ? 
                           "echo Command execution test" : "echo 'Command execution test'";
            
            System.out.println("执行命令: " + command);
            Process process = Runtime.getRuntime().exec(command);
            
            // 读取输出
            BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("  输出: " + line);
            }
            reader.close();
            
            int exitCode = process.waitFor();
            System.out.println("命令执行完成，退出码: " + exitCode);
            System.out.println("命令执行应该被RASP规则检测到");
            
        } catch (Exception e) {
            System.err.println("命令执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试混合操作
     */
    public static void testMixedOperations() {
        System.out.println("=== 测试混合操作 ===");
        
        try {
            // 1. 写入日志文件（应该被过滤）
            File logFile = new File("mixed-test.log");
            FileWriter logWriter = new FileWriter(logFile);
            logWriter.write("Log entry from mixed test\n");
            logWriter.close();
            System.out.println("写入日志文件（应该被过滤）");
            
            // 2. 写入普通文件（应该被检测）
            File normalFile = new File("mixed-test.txt");
            FileWriter normalWriter = new FileWriter(normalFile);
            normalWriter.write("Normal file from mixed test\n");
            normalWriter.close();
            System.out.println("写入普通文件（应该被检测）");
            
            // 3. 执行命令（应该被检测）
            String command = System.getProperty("os.name").toLowerCase().contains("windows") ? 
                           "echo Mixed test command" : "echo 'Mixed test command'";
            Process process = Runtime.getRuntime().exec(command);
            process.waitFor();
            System.out.println("执行命令（应该被检测）");
            
            // 清理
            logFile.delete();
            normalFile.delete();
            
        } catch (Exception e) {
            System.err.println("混合操作测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 主方法 - 运行所有测试
     */
    public static void main(String[] args) {
        System.out.println("RASP简化循环日志修复测试开始");
        System.out.println("========================================");
        
        // 运行各种测试
        testLogFileOperations();
        System.out.println();
        
        testLogsDirectoryOperations();
        System.out.println();
        
        testNormalFileOperations();
        System.out.println();
        
        testCommandExecution();
        System.out.println();
        
        testMixedOperations();
        System.out.println();
        
        System.out.println("========================================");
        System.out.println("简化循环日志修复测试完成");
        System.out.println();
        System.out.println("预期结果：");
        System.out.println("1. 日志文件操作（.log文件、logs/目录）应该被过滤，不触发规则");
        System.out.println("2. 普通文件操作应该被RASP规则检测到");
        System.out.println("3. 命令执行应该被RASP规则检测到");
        System.out.println("4. 不应该出现无限循环的DEBUG日志");
        System.out.println("5. 解决方案简单直接，易于理解和维护");
        
        // 等待观察
        try {
            System.out.println("\n等待3秒钟，观察是否有延迟的循环日志...");
            Thread.sleep(3000);
            System.out.println("观察完成！");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}

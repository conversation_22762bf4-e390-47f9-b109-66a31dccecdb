package examples;

import java.io.*;
import java.util.jar.JarFile;
import java.util.jar.JarEntry;
import java.util.Enumeration;

/**
 * JAR文件读取测试示例
 * 验证JAR文件读取是否会被正确过滤，不产生循环日志
 */
public class JarFileReadTestExample {
    
    /**
     * 测试直接读取JAR文件
     */
    public static void testDirectJarFileRead() {
        System.out.println("=== 测试直接JAR文件读取 ===");
        
        try {
            // 查找当前classpath中的JAR文件
            String classpath = System.getProperty("java.class.path");
            String[] paths = classpath.split(File.pathSeparator);
            
            String jarPath = null;
            for (String path : paths) {
                if (path.endsWith(".jar")) {
                    jarPath = path;
                    break;
                }
            }
            
            if (jarPath != null) {
                System.out.println("找到JAR文件: " + jarPath);
                
                // 直接读取JAR文件的字节
                File jarFile = new File(jarPath);
                if (jarFile.exists()) {
                    FileInputStream fis = new FileInputStream(jarFile);
                    byte[] buffer = new byte[1024];
                    int bytesRead = fis.read(buffer);
                    fis.close();
                    
                    System.out.println("读取JAR文件字节数: " + bytesRead);
                    System.out.println("这个操作应该被FileReadRule过滤，不产生循环日志");
                }
            } else {
                System.out.println("未找到JAR文件，跳过测试");
            }
            
        } catch (Exception e) {
            System.err.println("JAR文件读取测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试使用JarFile API读取JAR内容
     */
    public static void testJarFileAPI() {
        System.out.println("=== 测试JarFile API ===");
        
        try {
            // 查找JAR文件
            String classpath = System.getProperty("java.class.path");
            String[] paths = classpath.split(File.pathSeparator);
            
            String jarPath = null;
            for (String path : paths) {
                if (path.endsWith(".jar")) {
                    jarPath = path;
                    break;
                }
            }
            
            if (jarPath != null) {
                System.out.println("使用JarFile API读取: " + jarPath);
                
                JarFile jarFile = new JarFile(jarPath);
                Enumeration<JarEntry> entries = jarFile.entries();
                
                int entryCount = 0;
                while (entries.hasMoreElements() && entryCount < 5) {
                    JarEntry entry = entries.nextElement();
                    System.out.println("JAR条目: " + entry.getName());
                    entryCount++;
                }
                
                if (entryCount >= 5) {
                    System.out.println("... (还有更多条目)");
                }
                
                jarFile.close();
                System.out.println("JarFile API操作完成，应该被过滤");
            } else {
                System.out.println("未找到JAR文件，跳过测试");
            }
            
        } catch (Exception e) {
            System.err.println("JarFile API测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试RandomAccessFile读取JAR
     * 这是日志中显示的具体场景
     */
    public static void testRandomAccessFileJar() {
        System.out.println("=== 测试RandomAccessFile读取JAR ===");
        
        try {
            // 查找JAR文件
            String classpath = System.getProperty("java.class.path");
            String[] paths = classpath.split(File.pathSeparator);
            
            String jarPath = null;
            for (String path : paths) {
                if (path.endsWith(".jar")) {
                    jarPath = path;
                    break;
                }
            }
            
            if (jarPath != null) {
                System.out.println("使用RandomAccessFile读取JAR: " + jarPath);
                
                RandomAccessFile raf = new RandomAccessFile(jarPath, "r");
                
                // 读取文件头
                byte[] header = new byte[4];
                int bytesRead = raf.read(header);
                
                System.out.println("读取字节数: " + bytesRead);
                System.out.println("文件头: " + java.util.Arrays.toString(header));
                
                raf.close();
                System.out.println("RandomAccessFile JAR读取完成，应该被过滤");
            } else {
                System.out.println("未找到JAR文件，跳过测试");
            }
            
        } catch (Exception e) {
            System.err.println("RandomAccessFile JAR测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试读取类文件
     */
    public static void testClassFileRead() {
        System.out.println("=== 测试类文件读取 ===");
        
        try {
            // 创建一个临时的.class文件进行测试
            File tempClassFile = File.createTempFile("TestClass", ".class");
            
            // 写入一些模拟的类文件内容
            FileOutputStream fos = new FileOutputStream(tempClassFile);
            fos.write(new byte[]{(byte)0xCA, (byte)0xFE, (byte)0xBA, (byte)0xBE}); // Java class文件魔数
            fos.close();
            
            System.out.println("创建临时类文件: " + tempClassFile.getAbsolutePath());
            
            // 读取类文件
            FileInputStream fis = new FileInputStream(tempClassFile);
            byte[] buffer = new byte[4];
            int bytesRead = fis.read(buffer);
            fis.close();
            
            System.out.println("读取类文件字节数: " + bytesRead);
            System.out.println("类文件魔数: " + java.util.Arrays.toString(buffer));
            
            // 清理
            tempClassFile.delete();
            System.out.println("类文件读取完成，应该被过滤");
            
        } catch (Exception e) {
            System.err.println("类文件读取测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试普通文件读取（应该被检测）
     */
    public static void testNormalFileRead() {
        System.out.println("=== 测试普通文件读取 ===");
        
        try {
            // 创建一个普通文件
            File normalFile = File.createTempFile("normal_test", ".txt");
            
            // 写入内容
            FileWriter writer = new FileWriter(normalFile);
            writer.write("This is a normal text file\n");
            writer.write("It should be detected by RASP\n");
            writer.close();
            
            System.out.println("创建普通文件: " + normalFile.getAbsolutePath());
            
            // 读取普通文件
            BufferedReader reader = new BufferedReader(new FileReader(normalFile));
            String line;
            System.out.println("读取普通文件内容:");
            while ((line = reader.readLine()) != null) {
                System.out.println("  " + line);
            }
            reader.close();
            
            // 清理
            normalFile.delete();
            System.out.println("普通文件读取完成，应该被RASP检测到");
            
        } catch (Exception e) {
            System.err.println("普通文件读取测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 主方法 - 运行所有测试
     */
    public static void main(String[] args) {
        System.out.println("RASP JAR文件读取过滤测试开始");
        System.out.println("========================================");
        
        // 运行各种测试
        testDirectJarFileRead();
        System.out.println();
        
        testJarFileAPI();
        System.out.println();
        
        testRandomAccessFileJar();
        System.out.println();
        
        testClassFileRead();
        System.out.println();
        
        testNormalFileRead();
        System.out.println();
        
        System.out.println("========================================");
        System.out.println("JAR文件读取过滤测试完成");
        System.out.println();
        System.out.println("预期结果：");
        System.out.println("1. JAR文件读取操作应该被FileReadRule过滤，不产生循环日志");
        System.out.println("2. 类文件读取操作应该被过滤");
        System.out.println("3. 普通文件读取操作应该被RASP检测到");
        System.out.println("4. 不应该看到关于JAR文件的重复DEBUG日志");
        System.out.println("5. 应用启动时的JAR文件读取应该是静默的");
        
        // 等待观察
        try {
            System.out.println("\n等待3秒钟，观察是否有JAR文件相关的循环日志...");
            Thread.sleep(3000);
            System.out.println("观察完成！");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}

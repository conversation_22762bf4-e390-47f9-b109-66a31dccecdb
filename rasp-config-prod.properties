# RASP Agent Configuration File - Production Environment
# This configuration is optimized for production performance and security

# ============================================================================
# Global Configuration
# ============================================================================

rasp.enabled=true
rasp.log.level=WARN
rasp.log.file=logs/rasp-agent-prod.log

# ============================================================================
# Rule Configuration - Production Mode
# ============================================================================
# Focus on security rules, minimize verbose logging

# HTTP Request Logging Rule - ENABLED but less verbose in production
rasp.rule.HttpRequestLogRule.enabled=true

# Command Execution Security Rule - ENABLED for security protection
rasp.rule.CommandExecutionRule.enabled=true

# File Write Monitoring Rule - ENABLED for security monitoring
rasp.rule.FileWriteRule.enabled=true

# File Read Monitoring Rule - NEW: Prevent arbitrary file read attacks
rasp.rule.FileReadRule.enabled=false

# Method Call Logging Rule - DISABLED in production (too verbose)
rasp.rule.MethodCallLogRule.enabled=false

rasp.rule.MethodCallLogRule.enabled=false

# ============================================================================
# Hook Configuration
# ============================================================================

rasp.hook.http.enabled=true
rasp.hook.command.enabled=true
rasp.hook.file.enabled=true

# ============================================================================
# Performance Configuration - Optimized for Production
# ============================================================================

rasp.performance.max_events_per_second=500
rasp.performance.max_rule_execution_time=50
rasp.performance.monitoring.enabled=false

# ============================================================================
# Security Configuration
# ============================================================================

rasp.security.default_action=LOG_AND_BLOCK
rasp.security.alerts.enabled=true
rasp.security.alert_threshold=10

# ============================================================================
# Advanced Configuration - Production Settings
# ============================================================================

rasp.advanced.retransform_loaded_classes=true
rasp.advanced.max_enhance_batch_size=200

# Debug mode disabled for production
rasp.debug.enabled=false
rasp.debug.collect_stack_traces=false

# ============================================================================
# External Module Configuration
# ============================================================================

rasp.external.hooks.directory=/opt/rasp/hooks
rasp.external.rules.directory=/opt/rasp/rules
rasp.external.modules.enabled=true

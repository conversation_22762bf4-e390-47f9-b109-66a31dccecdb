package com.rasp.rules.file;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.FileWriteEvent;
import com.rasp.api.event.HttpRequestEvent;
import com.rasp.api.rule.AbstractRule;
import com.rasp.api.rule.RuleResult;
import com.rasp.api.context.RequestContext;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 增强版文件写入规则
 * 专门针对靶场攻击场景进行检测和分析
 */
public class FileWriteRule extends AbstractRule {
    
    // 敏感目录列表 - 增强版
    private static final List<String> SENSITIVE_DIRECTORIES = Arrays.asList(
        // Linux敏感目录
        "/etc", "/root", "/home", "/var/log", "/usr/bin", "/bin", "/sbin",
        "/var/www", "/var/www/html", "/opt", "/proc", "/sys",
        
        // Windows敏感目录
        "C:\\Windows", "C:\\System32", "C:\\Program Files", 
        "C:\\Program Files (x86)", "C:\\ProgramData", "C:\\Windows\\System32",
        
        // Web应用常见目录
        "/webapps", "/tomcat", "/apache", "/nginx", "/www", "/htdocs",
        "webapp", "webroot", "public_html"
    );
    
    // 危险文件扩展名 - 增强版
    private static final List<String> DANGEROUS_EXTENSIONS = Arrays.asList(
        // Web脚本文件
        "jsp", "jspx", "jspf", "php", "php3", "php4", "php5", "phtml", 
        "asp", "aspx", "cer", "cdx", "asa", "ashx",
        
        // 系统脚本文件
        "sh", "bash", "bat", "cmd", "ps1", "vbs", "vb", "py", "pl", "rb",
        
        // 可执行文件
        "exe", "com", "scr", "pif", "application", "gadget", "msi", "msp", "msc",
        
        // 配置文件
        "htaccess", "config", "conf", "ini", "xml", "properties"
    );
    
    // Web Shell特征关键词
    private static final List<String> WEBSHELL_KEYWORDS = Arrays.asList(
        "eval", "exec", "system", "shell_exec", "passthru", "Runtime.getRuntime", 
        "ProcessBuilder", "cmd", "powershell", "bash", "/bin/sh",
        "request.getParameter", "<%", "%>", "<?php", "?>", "<script",
        "CreateObject", "WScript.Shell", "Shell.Application"
    );
    
    // 路径穿越模式
    private static final Pattern PATH_TRAVERSAL_PATTERN = Pattern.compile(
        "(\\.\\./|\\.\\\\|%2e%2e%2f|%2e%2e%5c|\\.\\.%2f|\\.\\.%5c)", 
        Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public String getName() {
        return "FileWriteRule";
    }
    
    @Override
    public String getDescription() {
        return " rule to detect file write attacks targeting vulnerable applications";
    }
    
    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{HookEvent.EventType.FILE_WRITE};
    }

    /**
     * 检查是否为系统文件或应该被过滤的文件
     * 避免循环日志和减少误报
     */
    private boolean isSystemFile(String filePath) {
        if (filePath == null) {
            return false;
        }

        String lowerPath = filePath.toLowerCase();

        // 检查日志文件
        if (lowerPath.endsWith(".log")) {
            return true;
        }

        // 检查路径中是否包含日志相关目录
        if (lowerPath.contains("/logs/") ||
            lowerPath.contains("\\logs\\") ||
            lowerPath.contains("/log/") ||
            lowerPath.contains("\\log\\")) {
            return true;
        }

        // 检查临时文件
        if (lowerPath.endsWith(".tmp") ||
            lowerPath.endsWith(".temp") ||
            lowerPath.contains("/tmp/") ||
            lowerPath.contains("\\temp\\")) {
            return true;
        }

        // 检查系统目录
        if (lowerPath.contains("/lib/") ||
            lowerPath.contains("\\lib\\") ||
            lowerPath.contains("/jre/") ||
            lowerPath.contains("\\jre\\") ||
            lowerPath.contains("/jdk/") ||
            lowerPath.contains("\\jdk\\")) {
            return true;
        }

        // 检查是否为RASP相关文件
        if (lowerPath.contains("rasp-agent")) {
            return true;
        }

        return false;
    }

    @Override
    protected RuleResult doProcess(HookEvent event) {
        FileWriteEvent fileEvent = (FileWriteEvent) event;
        String filePath = fileEvent.getFilePath();

        // 简单检查：如果是系统文件或应该被过滤的文件，直接跳过
        if (isSystemFile(filePath)) {
            return RuleResult.allow(getName());
        }

        // 增强版日志记录（只对非内部操作记录）
        logger.debug("[FileWriteRule] ===== RULE PROCESSING STARTED =====");
        logger.debug("Event Type: {}", event.getEventType());
        logger.debug("Class: {}", fileEvent.getClassName());
        logger.debug("Method: {}", fileEvent.getMethodName());
        logger.debug("File Path: {}", filePath != null ? filePath : "NULL");
        logger.debug("Absolute Path: {}", fileEvent.getAbsolutePath());
        logger.debug("Is Absolute: {}", fileEvent.isAbsolutePath());
        logger.debug("File Extension: {}", fileEvent.getFileExtension());
        logger.debug("Timestamp: {}", new java.util.Date());

        if (filePath == null || filePath.trim().isEmpty()) {
            logger.debug("[FileWriteRule] Empty or null file path detected");
            return RuleResult.allow(getName());
        }

        logger.debug("[FileWriteRule] Analyzing file write operation: {}", filePath);

        // 获取HTTP请求上下文
        HttpRequestEvent httpRequest = RequestContext.getHttpRequestEvent();
        boolean hasHttpContext = httpRequest != null;
        
        if (hasHttpContext) {
            logger.debug("[FileWriteRule] HTTP Request Context Found!");
            printHttpRequestInfo(httpRequest);
        }

        // === 核心安全检查 ===
        
        // 1. 检查Web Shell上传 - 最高优先级
        RuleResult webShellResult = checkWebShellUpload(fileEvent, httpRequest);
        if (webShellResult.getAction() != RuleResult.Action.ALLOW) {
            return webShellResult;
        }
        
        // 2. 检查路径穿越攻击
        RuleResult pathTraversalResult = checkPathTraversal(fileEvent, httpRequest);
        if (pathTraversalResult.getAction() != RuleResult.Action.ALLOW) {
            return pathTraversalResult;
        }
        
        // 3. 检查敏感目录写入
        RuleResult sensitiveResult = checkSensitiveDirectories(fileEvent.getAbsolutePath());
        if (sensitiveResult.getAction() != RuleResult.Action.ALLOW) {
            return sensitiveResult;
        }
        
        // 4. 检查危险扩展名
        RuleResult extensionResult = checkDangerousExtensions(fileEvent);
        if (extensionResult.getAction() != RuleResult.Action.ALLOW) {
            return extensionResult;
        }
        
        // 5. 检查靶场特定攻击模式
        RuleResult targetSpecificResult = checkTargetSpecificPatterns(fileEvent, httpRequest);
        if (targetSpecificResult.getAction() != RuleResult.Action.ALLOW) {
            return targetSpecificResult;
        }

        // 6. 检查用户可控的绝对路径写入
        RuleResult controllablePathResult = checkControllableAbsolutePath(fileEvent, httpRequest);
        if (controllablePathResult.getAction() != RuleResult.Action.ALLOW) {
            return controllablePathResult;
        }

        logger.debug("[FileWriteRule] File write operation passed all security checks");
        logger.debug("[FileWriteRule] ===== RULE PROCESSING COMPLETED =====");

        return RuleResult.log(getName(), RuleResult.RiskLevel.LOW,
                            "File write operation monitored and logged: " + filePath);
    }
    
    /**
     * 检查Web Shell上传 - 核心防护功能
     */
    private RuleResult checkWebShellUpload(FileWriteEvent fileEvent, HttpRequestEvent httpRequest) {
        String filePath = fileEvent.getFilePath();
        String extension = fileEvent.getFileExtension();
        
        // 检查是否为Web脚本文件
        if (extension != null) {
            String lowerExt = extension.toLowerCase();
            
            // Web脚本扩展名检查
            List<String> webScriptExtensions = Arrays.asList("jsp", "jspx", "php", "asp", "aspx");
            if (webScriptExtensions.contains(lowerExt)) {
                
                // 如果有HTTP上下文，检查是否来自Web请求
                if (httpRequest != null) {
                    boolean fromHttpRequest = checkFilePathFromHttpRequest(filePath, httpRequest);
                    if (fromHttpRequest) {
                        String message = String.format(
                            "CRITICAL: Web Shell upload detected! Extension: .%s, Path: %s, HTTP Context: %s %s",
                            extension, filePath, httpRequest.getRequestMethod(), httpRequest.getRequestUrl()
                        );
                        logger.info("[SECURITY ALERT] {}", message);
                        return RuleResult.block(getName(), RuleResult.RiskLevel.CRITICAL, message);
                    }
                }
                
                // 即使没有HTTP上下文，Web脚本文件写入也需要告警
                String message = String.format("Web script file write: .%s file at %s", extension, filePath);
                logger.warn("[SECURITY ALERT] {}", message);
                return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH, message);
            }
        }
        
        return RuleResult.allow(getName());
    }
    
    /**
     * 检查路径穿越攻击
     */
    private RuleResult checkPathTraversal(FileWriteEvent fileEvent, HttpRequestEvent httpRequest) {
        String filePath = fileEvent.getFilePath();
        
        // 检查文件路径中的路径穿越字符
        if (PATH_TRAVERSAL_PATTERN.matcher(filePath).find()) {
            String message = String.format("Path traversal attack detected in file path: %s", filePath);
            logger.info("[SECURITY ALERT] {}", message);
            return RuleResult.block(getName(), RuleResult.RiskLevel.HIGH, message);
        }
        
        // 检查HTTP请求参数中的路径穿越
        if (httpRequest != null) {
            String queryString = httpRequest.getQueryString();
            if (queryString != null && PATH_TRAVERSAL_PATTERN.matcher(queryString).find()) {
                String message = String.format("Path traversal in query string: %s", queryString);
                logger.info("[SECURITY ALERT] {}", message);
                return RuleResult.block(getName(), RuleResult.RiskLevel.HIGH, message);
            }
        }
        
        return RuleResult.allow(getName());
    }
    
    /**
     * 检查敏感目录
     */
    private RuleResult checkSensitiveDirectories(String absolutePath) {
        if (absolutePath == null) {
            return RuleResult.allow(getName());
        }
        
        String lowerPath = absolutePath.toLowerCase();
        
        for (String sensitiveDir : SENSITIVE_DIRECTORIES) {
            if (lowerPath.startsWith(sensitiveDir.toLowerCase())) {
                String message = String.format("Writing to sensitive directory: %s in '%s'", sensitiveDir, absolutePath);
                logger.info("[SECURITY ALERT] {}", message);
                return RuleResult.block(getName(), RuleResult.RiskLevel.HIGH, message);
            }
        }
        
        return RuleResult.allow(getName());
    }
    
    /**
     * 检查危险文件扩展名
     */
    private RuleResult checkDangerousExtensions(FileWriteEvent fileEvent) {
        String extension = fileEvent.getFileExtension();
        if (extension == null) {
            return RuleResult.allow(getName());
        }
        
        String lowerExtension = extension.toLowerCase();
        
        if (DANGEROUS_EXTENSIONS.contains(lowerExtension)) {
            String message = String.format("Dangerous file type: .%s in '%s'", extension, fileEvent.getFilePath());
            logger.warn("[SECURITY ALERT] {}", message);
            
            // Web shell相关扩展名直接阻断
            if (Arrays.asList("jsp", "jspx", "php", "asp", "aspx").contains(lowerExtension)) {
                return RuleResult.block(getName(), RuleResult.RiskLevel.CRITICAL, 
                    "Potential web shell upload: " + message);
            } 
            // 系统脚本文件高危告警
            else if (Arrays.asList("sh", "bat", "cmd", "ps1", "exe").contains(lowerExtension)) {
                return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH, message);
            }
            // 其他危险文件中等告警
            else {
                return RuleResult.alert(getName(), RuleResult.RiskLevel.MEDIUM, message);
            }
        }
        
        return RuleResult.allow(getName());
    }
    
    /**
     * 检查用户可控的绝对路径写入
     * 只有当绝对路径来自HTTP请求时才告警，避免误报
     */
    private RuleResult checkControllableAbsolutePath(FileWriteEvent fileEvent, HttpRequestEvent httpRequest) {
        // 如果不是绝对路径，直接允许
        if (!fileEvent.isAbsolutePath()) {
            return RuleResult.allow(getName());
        }

        // 如果没有HTTP请求上下文，说明不是来自Web请求，允许
        if (httpRequest == null) {
            logger.debug("[FileWriteRule] Absolute path write without HTTP context (likely internal operation): {}",
                fileEvent.getAbsolutePath());
            return RuleResult.allow(getName());
        }

        String absolutePath = fileEvent.getAbsolutePath();
        String filePath = fileEvent.getFilePath();

        // 检查绝对路径或文件路径是否来自HTTP请求
        boolean pathFromRequest = checkFilePathFromHttpRequest(absolutePath, httpRequest) ||
                                 checkFilePathFromHttpRequest(filePath, httpRequest);

        if (pathFromRequest) {
            logger.warn("[FileWriteRule] USER-CONTROLLABLE ABSOLUTE PATH WRITE DETECTED!");
            String message = String.format(
                "User-controllable absolute path file write detected: %s (from HTTP request: %s %s)",
                absolutePath,
                httpRequest.getRequestMethod(),
                httpRequest.getRequestUrl()
            );

            // 用户可控的绝对路径写入是高风险行为
            return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH, message);
        } else {
            // 绝对路径写入但不是用户可控的，记录但不告警
            logger.debug("[FileWriteRule] Absolute path write (not user-controllable): {}", absolutePath);
            return RuleResult.allow(getName());
        }
    }

    /**
     * 检查靶场特定攻击模式
     */
    private RuleResult checkTargetSpecificPatterns(FileWriteEvent fileEvent, HttpRequestEvent httpRequest) {
        // 检查典型的靶场测试文件名
        String filePath = fileEvent.getFilePath();
        if (filePath != null) {
            String lowerPath = filePath.toLowerCase();
            
            // 检查常见的测试文件名
            if (lowerPath.contains("test.jsp") || lowerPath.contains("shell.jsp") || 
                lowerPath.contains("webshell") || lowerPath.contains("backdoor") ||
                lowerPath.contains("hack") || lowerPath.contains("exploit")) {
                
                String message = String.format("Suspicious test/attack file name: %s", filePath);
                logger.warn("[SECURITY ALERT] {}", message);
                return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH, message);
            }
        }
        
        return RuleResult.allow(getName());
    }

    /**
     * 打印HTTP请求信息 - 增强版
     */
    private void printHttpRequestInfo(HttpRequestEvent httpRequest) {
        logger.debug("=================== HTTP REQUEST ANALYSIS ===================");
        logger.debug("Request URL: " + httpRequest.getRequestUrl());
        logger.debug("Request Method: " + httpRequest.getRequestMethod());
        logger.debug("Request Path: " + httpRequest.getRequestPath());
        logger.debug("Query String: " + httpRequest.getQueryString());
        logger.debug("Client IP: " + httpRequest.getClientIp());
        logger.debug("User-Agent: " + httpRequest.getUserAgent());
        logger.debug("Session ID: " + httpRequest.getSessionId());

        // 打印请求参数
        if (httpRequest.getRequestParameters() != null && !httpRequest.getRequestParameters().isEmpty()) {
            logger.debug("Request Parameters:");
            for (java.util.Map.Entry<String, String[]> entry : httpRequest.getRequestParameters().entrySet()) {
                String paramName = entry.getKey();
                String[] paramValues = entry.getValue();
                if (paramValues != null) {
                    for (String value : paramValues) {
                        logger.debug("    " + paramName + " = " + value);
                        
                        // 检查参数中的Web Shell特征
                        if (value != null) {
                            for (String keyword : WEBSHELL_KEYWORDS) {
                                if (value.toLowerCase().contains(keyword.toLowerCase())) {
                                    logger.debug("    >>> WEBSHELL KEYWORD DETECTED: " + keyword);
                                }
                            }
                        }
                    }
                }
            }
        }

        // 打印请求体
        if (httpRequest.getRequestBody() != null && !httpRequest.getRequestBody().isEmpty()) {
            logger.debug("Request Body: " + httpRequest.getRequestBody());
            
            // 检查请求体中的Web Shell特征
            String body = httpRequest.getRequestBody().toLowerCase();
            for (String keyword : WEBSHELL_KEYWORDS) {
                if (body.contains(keyword.toLowerCase())) {
                    logger.debug(">>> WEBSHELL KEYWORD IN BODY: " + keyword);
                }
            }
        }

        logger.debug("=================== END OF HTTP ANALYSIS ===================");
    }

    /**
     * 检查文件路径是否来自HTTP请求参数 - 增强版
     * 支持路径的各种变体检测
     */
    private boolean checkFilePathFromHttpRequest(String filePath, HttpRequestEvent httpRequest) {
        if (filePath == null || httpRequest == null) {
            return false;
        }

        // 标准化路径用于比较
        String normalizedPath = normalizePath(filePath);

        // 检查查询字符串
        if (checkPathInString(filePath, normalizedPath, httpRequest.getQueryString(), "query string")) {
            return true;
        }

        // 检查请求参数
        if (httpRequest.getRequestParameters() != null) {
            for (java.util.Map.Entry<String, String[]> entry : httpRequest.getRequestParameters().entrySet()) {
                String paramName = entry.getKey();
                String[] paramValues = entry.getValue();
                if (paramValues != null) {
                    for (String value : paramValues) {
                        if (value != null) {
                            if (checkPathInString(filePath, normalizedPath, value, "parameter '" + paramName + "'")) {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        // 检查请求体
        if (checkPathInString(filePath, normalizedPath, httpRequest.getRequestBody(), "request body")) {
            return true;
        }

        return false;
    }

    /**
     * 标准化路径，处理不同的路径分隔符和编码
     */
    private String normalizePath(String path) {
        if (path == null) {
            return null;
        }

        // 统一路径分隔符
        String normalized = path.replace('\\', '/');

        // URL解码
        try {
            normalized = java.net.URLDecoder.decode(normalized, "UTF-8");
        } catch (Exception e) {
            // 解码失败，使用原始路径
        }

        return normalized.toLowerCase();
    }

    /**
     * 在字符串中检查路径的各种变体
     */
    private boolean checkPathInString(String originalPath, String normalizedPath, String searchIn, String location) {
        if (searchIn == null) {
            return false;
        }

        String searchInLower = searchIn.toLowerCase();
        String searchInNormalized = normalizePath(searchIn);

        // 1. 精确匹配原始路径
        if (searchIn.equals(originalPath)) {
            logger.debug("File path '{}' exactly matches in {}: {}", originalPath, location, searchIn);
            return true;
        }

        // 2. 包含匹配原始路径
        if (searchIn.contains(originalPath)) {
            logger.debug("File path '{}' found in {}: {}", originalPath, location, searchIn);
            return true;
        }

        // 3. 标准化路径匹配
        if (normalizedPath != null && searchInNormalized != null) {
            if (searchInNormalized.contains(normalizedPath)) {
                logger.debug("Normalized file path '{}' found in {}: {}", normalizedPath, location, searchIn);
                return true;
            }
        }

        // 4. 检查路径的文件名部分
        String fileName = extractFileName(originalPath);
        if (fileName != null && fileName.length() > 3) { // 避免太短的文件名误匹配
            if (searchInLower.contains(fileName.toLowerCase())) {
                logger.debug("File name '{}' from path '{}' found in {}: {}", fileName, originalPath, location, searchIn);
                return true;
            }
        }

        // 5. 检查路径的目录部分（如果是绝对路径）
        if (originalPath.startsWith("/") || originalPath.contains(":\\")) {
            String[] pathParts = originalPath.split("[/\\\\]");
            for (String part : pathParts) {
                if (part.length() > 2 && searchInLower.contains(part.toLowerCase())) {
                    logger.debug("Path component '{}' from '{}' found in {}: {}", part, originalPath, location, searchIn);
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 提取文件名
     */
    private String extractFileName(String path) {
        if (path == null) {
            return null;
        }

        int lastSlash = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));
        if (lastSlash >= 0 && lastSlash < path.length() - 1) {
            return path.substring(lastSlash + 1);
        }

        return path;
    }
} 
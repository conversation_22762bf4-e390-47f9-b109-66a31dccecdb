package com.rasp.core;

import com.rasp.core.classloader.RaspClassLoader;
import com.rasp.core.enhance.ClassEnhancer;
import com.rasp.core.hook.HookManager;
import com.rasp.core.rule.RuleEngine;
import com.rasp.core.config.RaspConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.lang.instrument.ClassFileTransformer;
import java.lang.instrument.IllegalClassFormatException;
import java.lang.instrument.Instrumentation;
import java.net.URL;
import java.security.ProtectionDomain;

/**
 * RASP核心类
 * 负责初始化和管理RASP的各个组件
 */
public class RaspCore {
    
    private static final Logger logger = LoggerFactory.getLogger(RaspCore.class);
    
    /**
     * RASP类加载器
     */
    private static RaspClassLoader raspClassLoader;
    
    /**
     * Instrumentation实例
     */
    private static Instrumentation instrumentation;
    
    /**
     * 是否已初始化
     */
    private static volatile boolean initialized = false;
    
    /**
     * 初始化RASP核心
     * @param inst Instrumentation实例
     * @param agentArgs Agent参数
     */
    public static synchronized void initialize(Instrumentation inst, String agentArgs) {
        if (initialized) {
            logger.warn("RASP already initialized");
            return;
        }
        
        try {
            instrumentation = inst;
            
            logger.info("Initializing RASP Core...");
            
            // 初始化类加载器
            initializeClassLoader();
            
            // 注册类转换器
            registerClassTransformer();

            // 初始化Hook管理器
            initializeHookManager();
            
            // 初始化规则引擎
            initializeRuleEngine();
            
            // 注册内置Hook和规则
            registerBuiltinHooksAndRules();

            // // 加载外部Hook和规则
            loadExternalModules();

            // 设置Spy回调
            setupSpyCallback();
            logger.info("Spy callback setup completed, proceeding to retransformation...");

            // 启用类重转换，处理已加载的类
            logger.info("Starting retransformation scheduling...");
            scheduleRetransformation();
            
            initialized = true;
            logger.info("RASP Core initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize RASP Core", e);
            throw new RuntimeException("RASP initialization failed", e);
        }
    }
    
    /**
     * 初始化类加载器
     */
    private static void initializeClassLoader() {
        try {
            // 获取当前JAR文件的URL
            URL[] urls = new URL[0]; // 可以根据需要添加额外的JAR路径
            
            raspClassLoader = new RaspClassLoader(urls, 
                Thread.currentThread().getContextClassLoader(), "default");
            
            logger.info("RASP ClassLoader initialized");
            
        } catch (Exception e) {
            logger.error("Failed to initialize RASP ClassLoader", e);
            throw new RuntimeException("ClassLoader initialization failed", e);
        }
    }
    
    /**
     * 注册类转换器
     */
    private static void registerClassTransformer() {
        ClassFileTransformer transformer = new ClassFileTransformer() {
            @Override
            public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined,
                                  ProtectionDomain protectionDomain, byte[] classfileBuffer)
                    throws IllegalClassFormatException {

                // 处理className为null的情况（Lambda表达式、动态生成的类等）
                if (className == null) {
                    return null; // 不处理匿名类
                }

                // 跳过RASP自身的类
                if (className.startsWith("com/rasp/")) {
                    return null;
                }

                // 跳过JDK核心类，但允许关键系统类进行增强
                if (className.startsWith("java/") ||
                    className.startsWith("javax/") ||
                    className.startsWith("sun/") ||
                    className.startsWith("com/sun/") ||
                    className.startsWith("jdk/")) {

                    // 检查是否是关键系统类，如果是则允许处理
                    // String normalizedClassName = className.replace('/', '.');
                    // if (!isKeySystemClass(normalizedClassName)) {
                    //     return null;
                    // }
                }

                // 跳过一些已知的框架内部类，避免不必要的处理
                if (className.startsWith("org/springframework/cglib/") ||
                    className.startsWith("net/sf/cglib/") ||
                    className.startsWith("org/apache/catalina/loader/") ||
                    className.startsWith("org/eclipse/jdt/") ||
                    // 日志相关类，绝对不能增强
                    className.startsWith("ch/qos/logback/") ||
                    className.startsWith("org/slf4j/") ||
                    className.startsWith("org/apache/logging/") ||
                    className.startsWith("java/util/logging/") ||
                    className.startsWith("org/apache/log4j/") ||
                    // 其他框架类
                    className.contains("$$") || // 通常是代理类
                    className.contains("$Proxy") || // JDK动态代理
                    className.contains("CGLIB$$") || // CGLIB代理
                    className.contains("ByteBuddy") || // ByteBuddy生成的类
                    className.contains("$Lambda$") || // Lambda表达式
                    // 暂时跳过一些复杂的Spring类，避免字节码验证错误
                    className.equals("org/springframework/web/servlet/DispatcherServlet") ||
                    className.startsWith("org/springframework/web/servlet/") ||
                    className.startsWith("org/springframework/boot/autoconfigure/") ||
                    // 跳过所有Apache Catalina相关类，避免影响Tomcat日志
                    className.startsWith("org/apache/catalina/") ||
                    className.startsWith("org/apache/tomcat/") ||
                    className.startsWith("org/apache/coyote/")) {
                    return null;
                }

                try {
                    // 检查是否需要增强这个类
                    String normalizedClassName = className.replace('/', '.');
                    HookManager hookManager = HookManager.getInstance();

                    // 添加调试信息：检查所有Spring相关的类和Servlet相关的类
                    if (className.contains("springframework") || className.contains("Dispatcher") ||
                        className.contains("servlet") || className.contains("Servlet")) {
                        logger.debug("[ClassTransformer] Checking class: {}", normalizedClassName);

                        java.util.List<?> hooks = hookManager.getHooksForClass(normalizedClassName);
                        logger.debug("[ClassTransformer] Hooks for {}: {}", normalizedClassName, hooks.size());

                        boolean isSafe = isSafeToEnhance(className);
                        logger.debug("[ClassTransformer] Is safe to enhance {}: {}", normalizedClassName, isSafe);

                        if (hooks.size() > 0) {
                            logger.debug("[ClassTransformer] Found hooks for {}, proceeding with enhancement", normalizedClassName);
                        }
                    }

                    java.util.List<?> hooks = hookManager.getHooksForClass(normalizedClassName);
                    if (!hooks.isEmpty()) {
                        // 检查是否是安全的类，可以进行字节码增强
                        if (isSafeToEnhance(className)) {
                            logger.debug("[ClassTransformer] Enhancing class: {} with {} hooks", normalizedClassName, hooks.size());
                            byte[] enhancedBytes = ClassEnhancer.enhance(normalizedClassName, classfileBuffer);
                            if (enhancedBytes != null && enhancedBytes != classfileBuffer) {
                                logger.debug("[ClassTransformer] Successfully enhanced class: {}", normalizedClassName);
                                return enhancedBytes;
                            } else {
                                logger.debug("[ClassTransformer] Enhancement returned original bytecode for: {}", normalizedClassName);
                            }
                        } else {
                            logger.debug("Skipping enhancement for potentially unsafe class: {}", normalizedClassName);
                        }
                    }

                } catch (Exception e) {
                    logger.warn("Error transforming class: {}, using original bytecode: {}", className, e.getMessage());
                }

                return null; // 不修改字节码
            }
        };

        instrumentation.addTransformer(transformer, true);
        logger.info("Class transformer registered");
    }

    /**
     * 检查是否是关键的系统类
     * @param className 类名
     * @return true表示是关键系统类
     */
    private static boolean isKeySystemClass(String className) {
        return className.equals("java.lang.Runtime") ||
               className.equals("java.lang.ProcessBuilder") ||
               className.equals("java.lang.Process") ||
               // 文件IO相关的关键类
               className.equals("java.io.FileInputStream") ||
               className.equals("java.io.FileOutputStream") ||
               className.equals("java.io.RandomAccessFile") ||
               className.equals("java.nio.file.Files") ||  // ← 新增：支持NIO Files类
               // 网络相关的关键类
               className.equals("java.net.Socket") ||
               className.equals("java.net.ServerSocket") ||
               // HTTP相关的关键类
               className.equals("javax.servlet.http.HttpServlet") ||
               className.equals("org.springframework.web.servlet.DispatcherServlet") ||
               className.equals("org.apache.catalina.core.ApplicationFilterChain") ||
               className.equals("org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter") ||
               className.equals("org.springframework.web.servlet.mvc.Controller");
    }

    /**
     * 检查类是否安全进行字节码增强
     * @param className 内部类名（使用/分隔）
     * @return true表示安全
     */
    private static boolean isSafeToEnhance(String className) {
        // 允许增强关键的系统类（用于安全监控）
        String normalizedClassName = className.replace('/', '.');
        if (isKeySystemClass(normalizedClassName)) {
            return true;
        }

        // 允许增强我们明确想要Hook的HTTP类
        if (normalizedClassName.equals("javax.servlet.http.HttpServlet") ||
            normalizedClassName.equals("org.springframework.web.servlet.DispatcherServlet") ||
            normalizedClassName.equals("org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter") ||
            normalizedClassName.equals("org.springframework.web.method.support.InvocableHandlerMethod") ||
            normalizedClassName.equals("org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod")) {
            // 暂时移除ApplicationFilterChain，因为它的字节码增强有问题
            // normalizedClassName.equals("org.apache.catalina.core.ApplicationFilterChain")) {
            return true;
        }

        // 绝对不能增强的类
        if (className.startsWith("org/springframework/web/servlet/") ||
            className.startsWith("org/springframework/boot/autoconfigure/") ||
            className.startsWith("org/apache/catalina/") ||
            className.startsWith("org/apache/tomcat/") ||
            className.startsWith("org/apache/coyote/") ||
            // 日志相关类绝对不能增强
            className.startsWith("ch/qos/logback/") ||
            className.startsWith("org/slf4j/") ||
            className.startsWith("org/apache/logging/") ||
            className.startsWith("java/util/logging/") ||
            className.startsWith("org/apache/log4j/") ||
            // 其他敏感类（但排除我们明确想要Hook的类）
            className.contains("Logger") ||
            className.contains("Appender")) {
            return false;
        }

        // 允许增强应用业务类
        if (className.startsWith("org/javaweb/vuln/") ||
            className.startsWith("com/example/") ||
            className.startsWith("com/test/")) {
            return true;
        }

        // 默认增强
        return true;
    }
    
    /**
     * 初始化Hook管理器
     */
    private static void initializeHookManager() {
        HookManager.getInstance();
        logger.info("Hook Manager initialized");
    }
    
    /**
     * 初始化规则引擎
     */
    private static void initializeRuleEngine() {
        RuleEngine.getInstance();
        logger.info("Rule Engine initialized");
    }
    
    /**
     * 注册内置Hook和规则
     */
    private static void registerBuiltinHooksAndRules() {
        try {
            HookManager hookManager = HookManager.getInstance();
            RuleEngine ruleEngine = RuleEngine.getInstance();

            // 自动扫描并注册Hook
            autoRegisterHooks(hookManager);

            // 自动扫描并注册Rule
            autoRegisterRules(ruleEngine);

            // 根据配置文件启用/禁用规则
            configureRulesFromConfig(ruleEngine);

            logger.info("Built-in hooks and rules registration completed");

        } catch (Exception e) {
            logger.error("Failed to register built-in hooks and rules", e);
        }
    }

    /**
     * 自动扫描并注册Hook
     */
    private static void autoRegisterHooks(HookManager hookManager) {
        // 定义要扫描的Hook包
        String[] hookPackages = {
            "com.rasp.hooks.http",
            "com.rasp.hooks.command",
            "com.rasp.hooks.file",
        };

        for (String packageName : hookPackages) {
            try {
                scanAndRegisterHooks(packageName, hookManager);
            } catch (Exception e) {
                logger.warn("Failed to scan package: " + packageName, e);
            }
        }
    }

    /**
     * 自动扫描并注册Rule
     */
    private static void autoRegisterRules(RuleEngine ruleEngine) {
        // 定义要扫描的Rule包
        String[] rulePackages = {
            "com.rasp.rules.command",
            "com.rasp.rules.http",
            "com.rasp.rules.file",
            "com.rasp.rules.deserialization",
        };

        for (String packageName : rulePackages) {
            try {
                scanAndRegisterRules(packageName, ruleEngine);
            } catch (Exception e) {
                logger.warn("Failed to scan package: " + packageName, e);
            }
        }
    }

    /**
     * 扫描指定包并注册Hook
     */
    private static void scanAndRegisterHooks(String packageName, HookManager hookManager) {
        try {
            // 获取包下的所有类
            String[] classNames = getClassNamesInPackage(packageName);

            for (String className : classNames) {
                try {
                    Class<?> clazz = Class.forName(className);

                    // 检查是否实现了Hook接口
                    if (com.rasp.api.hook.Hook.class.isAssignableFrom(clazz) &&
                        !clazz.isInterface() && !java.lang.reflect.Modifier.isAbstract(clazz.getModifiers())) {

                        Object hookInstance = clazz.newInstance();
                        hookManager.registerHook((com.rasp.api.hook.Hook) hookInstance);
                        logger.info("Auto-registered Hook: " + clazz.getSimpleName());
                    }
                } catch (Exception e) {
                    logger.warn("Failed to register Hook class: " + className, e);
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to scan Hook package: " + packageName, e);
        }
    }

    /**
     * 扫描指定包并注册Rule
     */
    private static void scanAndRegisterRules(String packageName, RuleEngine ruleEngine) {
        try {
            // 获取包下的所有类
            String[] classNames = getClassNamesInPackage(packageName);

            for (String className : classNames) {
                try {
                    Class<?> clazz = Class.forName(className);

                    // 检查是否实现了Rule接口
                    if (com.rasp.api.rule.Rule.class.isAssignableFrom(clazz) &&
                        !clazz.isInterface() && !java.lang.reflect.Modifier.isAbstract(clazz.getModifiers())) {

                        Object ruleInstance = clazz.newInstance();
                        ruleEngine.registerRule((com.rasp.api.rule.Rule) ruleInstance);
                        logger.info("Auto-registered Rule: " + clazz.getSimpleName());
                    }
                } catch (Exception e) {
                    logger.warn("Failed to register Rule class: " + className, e);
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to scan Rule package: " + packageName, e);
        }
    }

    /**
     * 获取指定包下的所有类名
     */
    private static String[] getClassNamesInPackage(String packageName) {
        try {
            // 将包名转换为路径
            String packagePath = packageName.replace('.', '/');

            // 尝试从当前ClassLoader获取资源
            ClassLoader classLoader = RaspCore.class.getClassLoader();
            java.net.URL packageURL = classLoader.getResource(packagePath);

            if (packageURL == null) {
                logger.warn("Package not found: " + packageName);
                return new String[0];
            }

            // 如果是JAR文件中的资源
            if (packageURL.getProtocol().equals("jar")) {
                return getClassNamesFromJar(packageURL, packageName);
            }
            // 如果是文件系统中的资源
            else if (packageURL.getProtocol().equals("file")) {
                return getClassNamesFromFileSystem(packageURL, packageName);
            }

        } catch (Exception e) {
            logger.warn("Failed to get class names in package: " + packageName, e);
        }

        return new String[0];
    }

    /**
     * 从JAR文件中获取类名
     */
    private static String[] getClassNamesFromJar(java.net.URL packageURL, String packageName) {
        java.util.List<String> classNames = new java.util.ArrayList<>();

        try {
            String jarPath = packageURL.getPath().substring(5, packageURL.getPath().indexOf("!"));
            java.util.jar.JarFile jarFile = new java.util.jar.JarFile(jarPath);
            java.util.Enumeration<java.util.jar.JarEntry> entries = jarFile.entries();

            String packagePath = packageName.replace('.', '/');

            while (entries.hasMoreElements()) {
                java.util.jar.JarEntry entry = entries.nextElement();
                String entryName = entry.getName();

                if (entryName.startsWith(packagePath) && entryName.endsWith(".class")) {
                    // 确保是直接在该包下的类，不包括子包
                    String relativePath = entryName.substring(packagePath.length() + 1);
                    if (!relativePath.contains("/")) {
                        String className = entryName.replace('/', '.').replace(".class", "");
                        classNames.add(className);
                    }
                }
            }

            jarFile.close();
        } catch (Exception e) {
            logger.warn("Failed to read JAR file for package: " + packageName, e);
        }

        return classNames.toArray(new String[0]);
    }

    /**
     * 从文件系统中获取类名
     */
    private static String[] getClassNamesFromFileSystem(java.net.URL packageURL, String packageName) {
        java.util.List<String> classNames = new java.util.ArrayList<>();

        try {
            java.io.File packageDir = new java.io.File(packageURL.toURI());
            java.io.File[] files = packageDir.listFiles();

            if (files != null) {
                for (java.io.File file : files) {
                    if (file.isFile() && file.getName().endsWith(".class")) {
                        String className = packageName + "." + file.getName().replace(".class", "");
                        classNames.add(className);
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to read file system for package: " + packageName, e);
        }

        return classNames.toArray(new String[0]);
    }
    
    /**
     * 加载外部模块（Hook和规则）
     */
    private static void loadExternalModules() {
        try {
            // 获取RASP_HOME环境变量或系统属性
            String raspHome = System.getProperty("rasp.home", System.getenv("RASP_HOME"));
            if (raspHome == null) {
                logger.info("RASP_HOME not set, skipping external module loading");
                return;
            }
            
            File raspHomeDir = new File(raspHome);
            if (!raspHomeDir.exists() || !raspHomeDir.isDirectory()) {
                logger.warn("RASP_HOME directory does not exist: {}", raspHome);
                return;
            }
            
            // 加载Hook模块
            File hooksDir = new File(raspHomeDir, "hooks");
            if (hooksDir.exists() && hooksDir.isDirectory()) {
                loadModulesFromDirectory(hooksDir, "hook");
            }
            
            // 加载规则模块
            File rulesDir = new File(raspHomeDir, "rules");
            if (rulesDir.exists() && rulesDir.isDirectory()) {
                loadModulesFromDirectory(rulesDir, "rule");
            }
            
        } catch (Exception e) {
            logger.error("Failed to load external modules", e);
        }
    }
    
    /**
     * 从目录加载模块
     * @param directory 目录
     * @param moduleType 模块类型（hook或rule）
     */
    private static void loadModulesFromDirectory(File directory, String moduleType) {
        File[] jarFiles = directory.listFiles((dir, name) -> name.endsWith(".jar"));
        if (jarFiles == null) {
            return;
        }
        
        for (File jarFile : jarFiles) {
            try {
                logger.info("Loading {} module from: {}", moduleType, jarFile.getName());
                
                // 这里需要从JAR文件的MANIFEST.MF或配置文件中读取主类名
                // 简化实现，假设类名遵循约定
                String className = inferClassNameFromJar(jarFile, moduleType);
                
                if ("hook".equals(moduleType)) {
                    HookManager.getInstance().loadHookFromJar(jarFile, className);
                } else if ("rule".equals(moduleType)) {
                    RuleEngine.getInstance().loadRuleFromJar(jarFile, className);
                }
                
            } catch (Exception e) {
                logger.error("Failed to load module from: " + jarFile.getName(), e);
            }
        }
    }
    
    /**
     * 从JAR文件名推断类名（简化实现）
     * @param jarFile JAR文件
     * @param moduleType 模块类型
     * @return 类名
     */
    private static String inferClassNameFromJar(File jarFile, String moduleType) {
        String fileName = jarFile.getName();
        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
        
        // 简化的命名约定：hook-xxx.jar -> com.rasp.hooks.xxx.XxxHook
        if ("hook".equals(moduleType)) {
            return "com.rasp.hooks." + baseName.toLowerCase() + "." + 
                   capitalize(baseName) + "Hook";
        } else {
            return "com.rasp.rules." + baseName.toLowerCase() + "." + 
                   capitalize(baseName) + "Rule";
        }
    }
    
    /**
     * 首字母大写
     * @param str 字符串
     * @return 首字母大写的字符串
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    /**
     * 根据配置文件配置规则的启用/禁用状态
     * @param ruleEngine 规则引擎
     */
    private static void configureRulesFromConfig(RuleEngine ruleEngine) {
        try {
            RaspConfigManager configManager = RaspConfigManager.getInstance();

            // 获取所有已注册的规则
            java.util.List<com.rasp.api.rule.Rule> allRules = ruleEngine.getAllRules();

            for (com.rasp.api.rule.Rule rule : allRules) {
                String ruleName = rule.getName();

                // 检查配置文件中是否指定了该规则的状态
                boolean shouldEnable = configManager.isRuleEnabled(ruleName);

                if (shouldEnable) {
                    ruleEngine.enableRule(ruleName);
                    logger.info("Rule enabled by config: {}", ruleName);
                } else {
                    ruleEngine.disableRule(ruleName);
                    logger.info("Rule disabled by config: {}", ruleName);
                }
            }

            logger.info("Rules configuration completed from config file");

        } catch (Exception e) {
            logger.error("Failed to configure rules from config file", e);
            // 如果配置失败，使用默认配置
            logger.info("Using default rule configuration");
            ruleEngine.disableRule("MethodCallLogRule"); // 保持原有的默认行为
        }
    }
    
    /**
     * 销毁RASP核心
     */
    public static synchronized void destroy() {
        if (!initialized) {
            return;
        }
        
        try {
            logger.info("Destroying RASP Core...");
            
            // 清理Hook管理器
            HookManager.getInstance().clear();
            
            // 清理规则引擎
            RuleEngine.getInstance().clear();
            
            // 清理类加载器
            if (raspClassLoader != null) {
                raspClassLoader.clearCache();
            }
            
            initialized = false;
            logger.info("RASP Core destroyed");
            
        } catch (Exception e) {
            logger.error("Error destroying RASP Core", e);
        }
    }
    
    /**
     * 获取RASP类加载器
     * @return RASP类加载器
     */
    public static RaspClassLoader getRaspClassLoader() {
        return raspClassLoader;
    }
    
    /**
     * 获取Instrumentation实例
     * @return Instrumentation实例
     */
    public static Instrumentation getInstrumentation() {
        return instrumentation;
    }
    
    /**
     * 检查是否已初始化
     * @return true表示已初始化
     */
    public static boolean isInitialized() {
        return initialized;
    }

    /**
     * 设置SpyCallbackImpl处理器，建立与Bootstrap ClassLoader中SpyCallbackImpl类的通信
     */
    private static void setupSpyCallback() {
        try {
            // 获取Bootstrap ClassLoader中的SpyCallbackImpl类
            Class<?> callbackImplClass = Class.forName("com.rasp.core.spy.SpyCallbackImpl", false, null);
            logger.info("Found SpyCallbackImpl class: {} (ClassLoader: {})", callbackImplClass, callbackImplClass.getClassLoader());

            // 设置实际的处理器
            SpyCallbackHandler actualHandler = new SpyCallbackHandler();
            java.lang.reflect.Method setActualHandlerMethod = callbackImplClass.getMethod("setActualHandler", Object.class);
            setActualHandlerMethod.invoke(null, actualHandler);
            logger.info("Set actual handler: {}", actualHandler);

            logger.info("SpyCallbackImpl setup completed");

        } catch (Exception e) {
            logger.error("Failed to setup SpyCallbackImpl", e);
            e.printStackTrace();
        }
    }

    /**
     * 调度重转换任务
     */
    private static void scheduleRetransformation() {
        logger.info("scheduleRetransformation() method called");
        // 在单独的线程中执行重转换，避免在Agent初始化时出现问题
        Thread retransformThread = new Thread(() -> {
            try {
                // 等待一段时间，确保Agent完全初始化
                Thread.sleep(1000);
                retransformLoadedClasses();
            } catch (InterruptedException e) {
                logger.warn("Retransformation thread interrupted");
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                logger.error("Error in retransformation thread", e);
            }
        }, "RASP-Retransform-Thread");

        retransformThread.setDaemon(true);
        retransformThread.start();
        logger.info("Retransformation scheduled");
    }

    /**
     * 重转换已加载的关键类
     */
    private static void retransformLoadedClasses() {
        if (!instrumentation.isRetransformClassesSupported()) {
            logger.warn("Retransform classes is not supported by this JVM");
            return;
        }

        try {
            HookManager hookManager = HookManager.getInstance();
            Class<?>[] loadedClasses = instrumentation.getAllLoadedClasses();

            logger.info("Starting retransformation of loaded classes...");
            int retransformedCount = 0;

            for (Class<?> clazz : loadedClasses) {
                try {
                    String className = clazz.getName();

                    // 添加调试信息：检查HTTP相关的类
                    if (className.contains("servlet") || className.contains("spring") || className.contains("tomcat") ||
                        className.contains("Servlet") || className.contains("Spring") || className.contains("Tomcat") ||
                        className.contains("http") || className.contains("Http") || className.contains("HTTP") ||
                        className.contains("Dispatcher") || className.contains("Filter") || className.contains("Request")) {
                        logger.debug("[Retransform] Found HTTP-related loaded class: {}", className);

                        // 检查是否有Hook匹配
                        java.util.List<?> hooks = hookManager.getHooksForClass(className);
                        logger.debug("[Retransform] Hooks for {}: {}", className, hooks.size());

                        // 检查是否是关键系统类
                        boolean isKeyClass = isKeySystemClass(className);
                        logger.debug("[Retransform] Is key system class {}: {}", className, isKeyClass);
                    }

                    // 检查是否需要重转换这个类
                    if (shouldRetransformClass(className, hookManager)) {
                        // 检查类是否可以重转换
                        if (instrumentation.isModifiableClass(clazz)) {
                            logger.info("Retransforming class: {}", className);
                            instrumentation.retransformClasses(clazz);
                            retransformedCount++;
                        } else {
                            logger.debug("Class {} is not modifiable, skipping retransformation", className);
                        }
                    }
                } catch (Exception e) {
                    logger.warn("Failed to retransform class: {}, error: {}", clazz.getName(), e.getMessage());
                }
            }

            logger.debug("Retransformation completed. {} classes retransformed.", retransformedCount);

        } catch (Exception e) {
            logger.error("Error during class retransformation", e);
        }
    }

    /**
     * 检查是否应该重转换指定的类
     * @param className 类名
     * @param hookManager Hook管理器
     * @return true表示应该重转换
     */
    private static boolean shouldRetransformClass(String className, HookManager hookManager) {
        // 检查是否有Hook匹配这个类
        if (!hookManager.getHooksForClass(className).isEmpty()) {
            // 只重转换关键的系统类
            if (isKeySystemClass(className)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Spy回调处理器
     */
    private static class SpyCallbackHandler implements java.lang.reflect.InvocationHandler {
        @Override
        public Object invoke(Object proxy, java.lang.reflect.Method method, Object[] args) throws Throwable {
            try {
                String methodName = method.getName();

                switch (methodName) {
                    case "onMethodEnter":
                        return handleOnMethodEnter((String) args[0], (String) args[1], (String) args[2], args[3], (Object[]) args[4]);
                    case "onMethodReturn":
                        handleOnMethodReturn((Object[]) args[0], args[1]);
                        return null;
                    case "onMethodThrow":
                        handleOnMethodThrow((Object[]) args[0], (Throwable) args[1]);
                        return null;
                    case "onMethodExit":
                        handleOnMethodExit((String) args[0], (String) args[1], (String) args[2], args[3]);
                        return null;
                    case "shouldHookClass":
                        return handleShouldHookClass((String) args[0]);
                    case "shouldHookMethod":
                        return handleShouldHookMethod((String) args[0], (String) args[1], (String) args[2]);
                    default:
                        return null;
                }
            } catch (Throwable t) {
                logger.error("Error in Spy callback: {}", t.getMessage());
                return null;
            }
        }

        public Object[] handleOnMethodEnter(String className, String methodName, String methodSignature, Object target, Object[] arguments) {
            try {
                logger.debug("[SpyCallback] handleOnMethodEnter called!");
                logger.debug("Class: {}", className);
                logger.debug("Method: {}", methodName);
                logger.debug("Signature: {}", methodSignature);

                HookManager hookManager = HookManager.getInstance();
                java.util.List<com.rasp.api.event.HookEvent> events = hookManager.onMethodEnter(className, methodName, methodSignature, target, arguments);

                logger.debug("HookManager returned {} events", events.size());

                return events.toArray();
            } catch (Exception e) {
                logger.error("Error in handleOnMethodEnter", e);
                e.printStackTrace();
                return new Object[0];
            }
        }

        public void handleOnMethodReturn(Object[] events, Object returnValue) {
            try {
                if (events != null && events.length > 0) {
                    HookManager hookManager = HookManager.getInstance();
                    java.util.List<com.rasp.api.event.HookEvent> eventList = new java.util.ArrayList<>();
                    for (Object event : events) {
                        if (event instanceof com.rasp.api.event.HookEvent) {
                            eventList.add((com.rasp.api.event.HookEvent) event);
                        }
                    }
                    hookManager.onMethodReturn(eventList, returnValue);
                }
            } catch (Exception e) {
                logger.error("Error in handleOnMethodReturn", e);
            }
        }

        public void handleOnMethodThrow(Object[] events, Throwable throwable) {
            try {
                if (events != null && events.length > 0) {
                    HookManager hookManager = HookManager.getInstance();
                    java.util.List<com.rasp.api.event.HookEvent> eventList = new java.util.ArrayList<>();
                    for (Object event : events) {
                        if (event instanceof com.rasp.api.event.HookEvent) {
                            eventList.add((com.rasp.api.event.HookEvent) event);
                        }
                    }
                    hookManager.onMethodThrow(eventList, throwable);
                }
            } catch (Exception e) {
                logger.error("Error in handleOnMethodThrow", e);
            }
        }

        public void handleOnMethodExit(String className, String methodName, String methodSignature, Object returnValue) {
            // 简化处理
        }

        public boolean handleShouldHookClass(String className) {
            try {
                HookManager hookManager = HookManager.getInstance();
                return !hookManager.getHooksForClass(className).isEmpty();
            } catch (Exception e) {
                return false;
            }
        }

        public boolean handleShouldHookMethod(String className, String methodName, String methodSignature) {
            try {
                HookManager hookManager = HookManager.getInstance();
                return !hookManager.getHooksForMethod(className, methodName, methodSignature).isEmpty();
            } catch (Exception e) {
                return false;
            }
        }
    }

}

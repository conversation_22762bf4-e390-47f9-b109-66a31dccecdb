# RASP反序列化攻击检测配置文件

# 是否启用反序列化检测规则
deserialization.rule.enabled=true

# 是否启用阻断模式（false表示仅记录日志）
deserialization.blocking.enabled=true

# 最低阻断风险等级 (LOW, MEDIUM, HIGH, CRITICAL)
# 设置为CRITICAL以减少误报，只阻断最危险的攻击
deserialization.min.block.risk.level=CRITICAL

# 是否记录详细的调用栈信息
deserialization.log.detailed.stack=true

# 调用栈分析的最大深度
deserialization.stack.analysis.max.depth=50

# 自定义危险方法黑名单（格式：类名.方法名=风险等级）
# 示例：
# custom.dangerous.method.1=com.example.CustomDeserializer.deserialize=HIGH
# custom.dangerous.method.2=com.example.UnsafeMethod.process=CRITICAL

# 日志配置
deserialization.log.security.alerts=true
deserialization.log.level=WARN

# 性能配置
deserialization.cache.enabled=true
deserialization.cache.max.size=1000

# 统计信息
deserialization.stats.enabled=true
deserialization.stats.log.interval=300000

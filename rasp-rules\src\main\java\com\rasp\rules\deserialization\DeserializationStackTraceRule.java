package com.rasp.rules.deserialization;

import com.rasp.api.event.CommandExecutionEvent;
import com.rasp.api.event.FileWriteEvent;
import com.rasp.api.event.FileReadEvent;
import com.rasp.api.event.HookEvent;
import com.rasp.api.rule.AbstractRule;
import com.rasp.api.rule.RuleResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 反序列化调用栈检测规则
 * 当检测到命令执行或文件操作时，回溯检查调用栈中是否包含危险的反序列化方法
 */
public class DeserializationStackTraceRule extends AbstractRule {

    private static final Logger logger = LoggerFactory.getLogger(DeserializationStackTraceRule.class);

    /**
     * 线程本地变量，用于防止递归调用
     */
    private static final ThreadLocal<Boolean> PROCESSING = new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };

    /**
     * 危险反序列化方法黑名单
     */
    private final Set<String> dangerousDeserializationMethods;

    /**
     * 风险等级映射
     */
    private final Map<String, RuleResult.RiskLevel> riskLevelMap;

    /**
     * 是否启用阻断模式
     */
    private boolean blockingEnabled = true;

    /**
     * 最低阻断风险等级
     */
    private RuleResult.RiskLevel minBlockRiskLevel = RuleResult.RiskLevel.HIGH;

    /**
     * RASP内部类名模式，用于过滤递归调用
     */
    private static final Set<String> RASP_INTERNAL_PATTERNS;

    static {
        RASP_INTERNAL_PATTERNS = new HashSet<>();
        RASP_INTERNAL_PATTERNS.add("com.rasp.");
        RASP_INTERNAL_PATTERNS.add("org.slf4j.");
        RASP_INTERNAL_PATTERNS.add("ch.qos.logback.");
        RASP_INTERNAL_PATTERNS.add("java.util.logging.");
        RASP_INTERNAL_PATTERNS.add("org.apache.logging.");
        RASP_INTERNAL_PATTERNS.add("org.apache.log4j.");
    }
    
    public DeserializationStackTraceRule() {
        super();
        this.dangerousDeserializationMethods = new HashSet<>();
        this.riskLevelMap = new HashMap<>();
        initializeBlacklist();
        // 使用System.out避免在初始化时触发日志写入
        System.out.println("RASP: DeserializationStackTraceRule initialized with " +
                          dangerousDeserializationMethods.size() + " dangerous methods");
    }
    
    @Override
    public String getName() {
        return "DeserializationStackTraceRule";
    }
    
    @Override
    public String getDescription() {
        return "Detects deserialization attacks by analyzing call stack when command execution or file operations occur";
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{
            HookEvent.EventType.COMMAND_EXECUTION,
            HookEvent.EventType.FILE_WRITE,
            HookEvent.EventType.FILE_READ
        };
    }
    
    /**
     * 初始化危险方法黑名单
     */
    private void initializeBlacklist() {
        // Java原生反序列化相关
        addDangerousMethod("java.io.ObjectInputStream.readObject", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("java.io.ObjectInputStream.readUnshared", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("java.io.ObjectInputStream.defaultReadObject", RuleResult.RiskLevel.MEDIUM);
        
        // Apache Commons Collections - 最危险的gadget chain
        addDangerousMethod("org.apache.commons.collections.functors.InvokerTransformer.transform", RuleResult.RiskLevel.CRITICAL);
        addDangerousMethod("org.apache.commons.collections.functors.InstantiateTransformer.transform", RuleResult.RiskLevel.CRITICAL);
        addDangerousMethod("org.apache.commons.collections4.functors.InvokerTransformer.transform", RuleResult.RiskLevel.CRITICAL);
        addDangerousMethod("org.apache.commons.collections4.functors.InstantiateTransformer.transform", RuleResult.RiskLevel.CRITICAL);
        
        // Apache Commons BeanUtils
        addDangerousMethod("org.apache.commons.beanutils.PropertyUtils.getProperty", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("org.apache.commons.beanutils.BeanUtils.populate", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("org.apache.commons.beanutils.BeanUtils.setProperty", RuleResult.RiskLevel.HIGH);
        
        // Spring Framework
        addDangerousMethod("org.springframework.beans.BeanUtils.instantiateClass", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("org.springframework.util.ReflectionUtils.invokeMethod", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("org.springframework.beans.BeanWrapperImpl.setPropertyValue", RuleResult.RiskLevel.HIGH);
        
        // Fastjson
        addDangerousMethod("com.alibaba.fastjson.JSON.parseObject", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("com.alibaba.fastjson.JSON.parse", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("com.alibaba.fastjson2.JSON.parseObject", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("com.alibaba.fastjson2.JSON.parse", RuleResult.RiskLevel.HIGH);
        
        // Jackson
        addDangerousMethod("com.fasterxml.jackson.databind.ObjectMapper.readValue", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("com.fasterxml.jackson.databind.ObjectMapper.readTree", RuleResult.RiskLevel.MEDIUM);
        
        // XStream
        addDangerousMethod("com.thoughtworks.xstream.XStream.fromXML", RuleResult.RiskLevel.HIGH);
        
        // Kryo
        addDangerousMethod("com.esotericsoftware.kryo.Kryo.readObject", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("com.esotericsoftware.kryo.Kryo.readClassAndObject", RuleResult.RiskLevel.MEDIUM);
        
        // Hessian
        addDangerousMethod("com.caucho.hessian.io.HessianInput.readObject", RuleResult.RiskLevel.MEDIUM);
        
        // 反射相关危险方法
        addDangerousMethod("java.lang.reflect.Method.invoke", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("java.lang.Class.newInstance", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("java.lang.reflect.Constructor.newInstance", RuleResult.RiskLevel.MEDIUM);
        
        // JNDI相关
        addDangerousMethod("javax.naming.InitialContext.lookup", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("javax.naming.Context.lookup", RuleResult.RiskLevel.HIGH);
        
        // 其他常见的gadget chain方法
        addDangerousMethod("java.util.PriorityQueue.readObject", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("java.util.Comparator.compare", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("sun.reflect.annotation.AnnotationInvocationHandler.readObject", RuleResult.RiskLevel.HIGH);
    }
    
    /**
     * 添加危险方法到黑名单
     */
    private void addDangerousMethod(String method, RuleResult.RiskLevel riskLevel) {
        dangerousDeserializationMethods.add(method);
        riskLevelMap.put(method, riskLevel);
    }
    
    @Override
    protected RuleResult doProcess(HookEvent event) {
        // 防止递归调用
        if (PROCESSING.get()) {
            return RuleResult.allow(getName());
        }

        // 检查是否为RASP内部操作（如日志写入）
        if (isRaspInternalOperation(event)) {
            return RuleResult.allow(getName());
        }

        try {
            PROCESSING.set(true);

            // 分析调用栈
            StackTraceAnalysisResult analysisResult = analyzeStackTrace(event);

            if (analysisResult.isDangerous()) {
                return handleDangerousDeserialization(event, analysisResult);
            }

            // 没有检测到危险的反序列化调用
            return RuleResult.allow(getName());

        } catch (Exception e) {
            // 使用System.err避免触发日志写入
            System.err.println("Error in DeserializationStackTraceRule: " + e.getMessage());
            return RuleResult.allow(getName());
        } finally {
            PROCESSING.set(false);
        }
    }

    /**
     * 检查是否为RASP内部操作
     */
    private boolean isRaspInternalOperation(HookEvent event) {
        // 检查触发事件的类名
        String className = event.getClassName();
        if (className != null) {
            for (String pattern : RASP_INTERNAL_PATTERNS) {
                if (className.startsWith(pattern)) {
                    return true;
                }
            }
        }

        // 检查调用栈中是否主要是RASP内部调用
        StackTraceElement[] stackTrace = event.getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            int raspInternalCount = 0;
            int totalRelevantFrames = Math.min(10, stackTrace.length); // 只检查前10层

            for (int i = 0; i < totalRelevantFrames; i++) {
                String frameClassName = stackTrace[i].getClassName();
                for (String pattern : RASP_INTERNAL_PATTERNS) {
                    if (frameClassName.startsWith(pattern)) {
                        raspInternalCount++;
                        break;
                    }
                }
            }

            // 如果超过一半的调用栈是RASP内部调用，认为是内部操作
            return raspInternalCount > totalRelevantFrames / 2;
        }

        return false;
    }

    /**
     * 分析调用栈
     */
    private StackTraceAnalysisResult analyzeStackTrace(HookEvent event) {
        StackTraceElement[] stackTrace = event.getStackTrace();
        if (stackTrace == null || stackTrace.length == 0) {
            return new StackTraceAnalysisResult();
        }
        
        StackTraceAnalysisResult result = new StackTraceAnalysisResult();
        
        // 遍历调用栈，查找危险方法
        for (int i = 0; i < stackTrace.length; i++) {
            StackTraceElement element = stackTrace[i];
            String className = element.getClassName();
            String methodName = element.getMethodName();
            String fullMethodName = className + "." + methodName;
            
            // 检查是否匹配黑名单
            if (dangerousDeserializationMethods.contains(fullMethodName)) {
                result.setDangerous(true);
                result.setDangerousMethod(fullMethodName);
                result.setStackIndex(i);
                result.setRiskLevel(riskLevelMap.getOrDefault(fullMethodName, RuleResult.RiskLevel.MEDIUM));

                // 使用System.err避免递归
                if (logger.isDebugEnabled()) {
                    System.err.println("RASP DEBUG: Found dangerous deserialization method: " + fullMethodName + " at index " + i);
                }
                break;
            }
        }
        
        return result;
    }
    
    /**
     * 处理检测到的危险反序列化
     */
    private RuleResult handleDangerousDeserialization(HookEvent event, StackTraceAnalysisResult analysisResult) {
        String dangerousMethod = analysisResult.getDangerousMethod();
        RuleResult.RiskLevel riskLevel = analysisResult.getRiskLevel();
        
        // 构建告警信息
        String alertMessage = buildAlertMessage(event, analysisResult);
        
        // 记录安全告警
        logSecurityAlert(event, analysisResult, alertMessage);
        
        // 根据风险等级和配置决定是否阻断
        if (shouldBlock(riskLevel)) {
            // 使用System.err避免触发文件写入Hook
            System.err.println("RASP SECURITY ALERT: BLOCKING deserialization attack - " +
                             event.getEventType() + " -> " + dangerousMethod + " (risk: " + riskLevel + ")");

            return RuleResult.block(getName(), riskLevel, alertMessage);
        } else {
            // 使用System.err避免触发文件写入Hook
            System.err.println("RASP SECURITY ALERT: DETECTED deserialization attack - " +
                             event.getEventType() + " -> " + dangerousMethod + " (risk: " + riskLevel + ")");

            return RuleResult.log(getName(), riskLevel, alertMessage);
        }
    }
    
    /**
     * 构建告警信息
     */
    private String buildAlertMessage(HookEvent event, StackTraceAnalysisResult analysisResult) {
        StringBuilder message = new StringBuilder();
        message.append("Potential Deserialization Attack Detected\n");
        message.append("Trigger Event: ").append(event.getEventType()).append("\n");
        message.append("Dangerous Method: ").append(analysisResult.getDangerousMethod()).append("\n");
        message.append("Risk Level: ").append(analysisResult.getRiskLevel()).append("\n");
        message.append("Stack Position: ").append(analysisResult.getStackIndex()).append("\n");
        message.append("Target Class: ").append(event.getClassName()).append("\n");
        message.append("Target Method: ").append(event.getMethodName()).append("\n");
        message.append("Thread: ").append(event.getThreadName()).append(" (").append(event.getThreadId()).append(")\n");
        message.append("Timestamp: ").append(new Date(event.getTimestamp())).append("\n");
        
        // 添加具体的触发信息
        if (event instanceof CommandExecutionEvent) {
            CommandExecutionEvent cmdEvent = (CommandExecutionEvent) event;
            message.append("Command: ").append(cmdEvent.getCommand()).append("\n");
        } else if (event instanceof FileWriteEvent) {
            FileWriteEvent fileEvent = (FileWriteEvent) event;
            message.append("File Path: ").append(fileEvent.getFilePath()).append("\n");
        } else if (event instanceof FileReadEvent) {
            FileReadEvent fileEvent = (FileReadEvent) event;
            message.append("File Path: ").append(fileEvent.getFilePath()).append("\n");
        }
        
        // 添加调用栈摘要
        StackTraceElement[] stackTrace = event.getStackTrace();
        if (stackTrace != null && stackTrace.length > analysisResult.getStackIndex()) {
            message.append("Call Stack Summary:\n");
            int startIndex = analysisResult.getStackIndex();
            int maxLines = Math.min(5, stackTrace.length - startIndex);
            for (int i = startIndex; i < startIndex + maxLines; i++) {
                message.append("  ").append(stackTrace[i].toString()).append("\n");
            }
        }
        
        return message.toString();
    }
    
    /**
     * 记录安全告警
     */
    private void logSecurityAlert(HookEvent event, StackTraceAnalysisResult analysisResult, String alertMessage) {
        // 使用System.err直接输出，避免触发日志框架的文件写入
        System.err.println("=== RASP DESERIALIZATION ATTACK DETECTED ===");
        System.err.println("Trigger: " + event.getEventType() + " -> " + analysisResult.getDangerousMethod());
        System.err.println("Risk Level: " + analysisResult.getRiskLevel());
        System.err.println("Thread: " + event.getThreadName() + " (" + event.getThreadId() + ")");
        System.err.println("Timestamp: " + new java.util.Date(event.getTimestamp()));
        System.err.println("Details:");
        System.err.println(alertMessage);
        System.err.println("=== END ALERT ===");
        System.err.flush();
    }
    
    /**
     * 判断是否应该阻断
     */
    private boolean shouldBlock(RuleResult.RiskLevel riskLevel) {
        if (!blockingEnabled) {
            return false;
        }
        
        // 根据风险等级判断
        switch (minBlockRiskLevel) {
            case LOW:
                return true;
            case MEDIUM:
                return riskLevel != RuleResult.RiskLevel.LOW;
            case HIGH:
                return riskLevel == RuleResult.RiskLevel.HIGH || 
                       riskLevel == RuleResult.RiskLevel.CRITICAL;
            case CRITICAL:
                return riskLevel == RuleResult.RiskLevel.CRITICAL;
            default:
                return false;
        }
    }
    
    /**
     * 调用栈分析结果
     */
    private static class StackTraceAnalysisResult {
        private boolean dangerous = false;
        private String dangerousMethod;
        private int stackIndex = -1;
        private RuleResult.RiskLevel riskLevel = RuleResult.RiskLevel.LOW;
        
        public boolean isDangerous() {
            return dangerous;
        }
        
        public void setDangerous(boolean dangerous) {
            this.dangerous = dangerous;
        }
        
        public String getDangerousMethod() {
            return dangerousMethod;
        }
        
        public void setDangerousMethod(String dangerousMethod) {
            this.dangerousMethod = dangerousMethod;
        }
        
        public int getStackIndex() {
            return stackIndex;
        }
        
        public void setStackIndex(int stackIndex) {
            this.stackIndex = stackIndex;
        }
        
        public RuleResult.RiskLevel getRiskLevel() {
            return riskLevel;
        }
        
        public void setRiskLevel(RuleResult.RiskLevel riskLevel) {
            this.riskLevel = riskLevel;
        }
    }
    
    /**
     * 设置阻断模式
     */
    public void setBlockingEnabled(boolean blockingEnabled) {
        this.blockingEnabled = blockingEnabled;
        logger.info("DeserializationStackTraceRule blocking mode set to: {}", blockingEnabled);
    }
    
    /**
     * 设置最低阻断风险等级
     */
    public void setMinBlockRiskLevel(RuleResult.RiskLevel minBlockRiskLevel) {
        this.minBlockRiskLevel = minBlockRiskLevel;
        logger.info("DeserializationStackTraceRule minimum block risk level set to: {}", minBlockRiskLevel);
    }
    
    /**
     * 添加自定义危险方法
     */
    public void addCustomDangerousMethod(String method, RuleResult.RiskLevel riskLevel) {
        dangerousDeserializationMethods.add(method);
        riskLevelMap.put(method, riskLevel);
        logger.info("Added custom dangerous method: {} (risk: {})", method, riskLevel);
    }
    
    /**
     * 获取黑名单统计信息
     */
    public Map<String, Object> getBlacklistStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalMethods", dangerousDeserializationMethods.size());
        stats.put("blockingEnabled", blockingEnabled);
        stats.put("minBlockRiskLevel", minBlockRiskLevel);
        
        // 按风险等级统计
        Map<RuleResult.RiskLevel, Integer> riskStats = new HashMap<>();
        for (RuleResult.RiskLevel level : riskLevelMap.values()) {
            riskStats.put(level, riskStats.getOrDefault(level, 0) + 1);
        }
        stats.put("riskLevelDistribution", riskStats);
        
        return stats;
    }
}

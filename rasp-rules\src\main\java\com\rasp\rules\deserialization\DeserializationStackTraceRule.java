package com.rasp.rules.deserialization;

import com.rasp.api.event.CommandExecutionEvent;
import com.rasp.api.event.FileWriteEvent;
import com.rasp.api.event.FileReadEvent;
import com.rasp.api.event.HookEvent;
import com.rasp.api.rule.AbstractRule;
import com.rasp.api.rule.RuleResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 反序列化调用栈检测规则
 * 当检测到命令执行或文件操作时，回溯检查调用栈中是否包含危险的反序列化方法
 */
public class DeserializationStackTraceRule extends AbstractRule {

    private static final Logger logger = LoggerFactory.getLogger(DeserializationStackTraceRule.class);

    /**
     * 危险反序列化方法黑名单
     */
    private final Set<String> dangerousDeserializationMethods;

    /**
     * 风险等级映射
     */
    private final Map<String, RuleResult.RiskLevel> riskLevelMap;

    /**
     * 是否启用阻断模式
     */
    private boolean blockingEnabled = true;

    /**
     * 最低阻断风险等级 - 设置为CRITICAL以减少误报
     */
    private RuleResult.RiskLevel minBlockRiskLevel = RuleResult.RiskLevel.CRITICAL;


    
    public DeserializationStackTraceRule() {
        super();
        this.dangerousDeserializationMethods = new HashSet<>();
        this.riskLevelMap = new HashMap<>();
        initializeBlacklist();
        logger.info("DeserializationStackTraceRule initialized with {} dangerous methods",
                   dangerousDeserializationMethods.size());
    }
    
    @Override
    public String getName() {
        return "DeserializationStackTraceRule";
    }
    
    @Override
    public String getDescription() {
        return "Detects deserialization attacks by analyzing call stack when command execution or file operations occur";
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{
            HookEvent.EventType.COMMAND_EXECUTION,
            HookEvent.EventType.FILE_WRITE,
            HookEvent.EventType.FILE_READ
        };
    }
    
    /**
     * 初始化危险方法黑名单
     */
    private void initializeBlacklist() {
        // Java原生反序列化相关
        addDangerousMethod("java.io.ObjectInputStream.readObject", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("java.io.ObjectInputStream.readUnshared", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("java.io.ObjectInputStream.defaultReadObject", RuleResult.RiskLevel.MEDIUM);
        
        // Apache Commons Collections - 最危险的gadget chain
        addDangerousMethod("org.apache.commons.collections.functors.InvokerTransformer.transform", RuleResult.RiskLevel.CRITICAL);
        addDangerousMethod("org.apache.commons.collections.functors.InstantiateTransformer.transform", RuleResult.RiskLevel.CRITICAL);
        addDangerousMethod("org.apache.commons.collections4.functors.InvokerTransformer.transform", RuleResult.RiskLevel.CRITICAL);
        addDangerousMethod("org.apache.commons.collections4.functors.InstantiateTransformer.transform", RuleResult.RiskLevel.CRITICAL);
        
        // Apache Commons BeanUtils
        addDangerousMethod("org.apache.commons.beanutils.PropertyUtils.getProperty", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("org.apache.commons.beanutils.BeanUtils.populate", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("org.apache.commons.beanutils.BeanUtils.setProperty", RuleResult.RiskLevel.HIGH);
        
        // Spring Framework
        addDangerousMethod("org.springframework.beans.BeanUtils.instantiateClass", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("org.springframework.util.ReflectionUtils.invokeMethod", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("org.springframework.beans.BeanWrapperImpl.setPropertyValue", RuleResult.RiskLevel.HIGH);
        
        // Fastjson
        addDangerousMethod("com.alibaba.fastjson.JSON.parseObject", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("com.alibaba.fastjson.JSON.parse", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("com.alibaba.fastjson2.JSON.parseObject", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("com.alibaba.fastjson2.JSON.parse", RuleResult.RiskLevel.HIGH);
        
        // Jackson
        addDangerousMethod("com.fasterxml.jackson.databind.ObjectMapper.readValue", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("com.fasterxml.jackson.databind.ObjectMapper.readTree", RuleResult.RiskLevel.MEDIUM);
        
        // XStream
        addDangerousMethod("com.thoughtworks.xstream.XStream.fromXML", RuleResult.RiskLevel.HIGH);
        
        // Kryo
        addDangerousMethod("com.esotericsoftware.kryo.Kryo.readObject", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("com.esotericsoftware.kryo.Kryo.readClassAndObject", RuleResult.RiskLevel.MEDIUM);
        
        // Hessian
        addDangerousMethod("com.caucho.hessian.io.HessianInput.readObject", RuleResult.RiskLevel.MEDIUM);
        
        // 反射相关危险方法 - 移除过于常见的Method.invoke，只保留真正危险的
        // addDangerousMethod("java.lang.reflect.Method.invoke", RuleResult.RiskLevel.MEDIUM); // 太常见，容易误报
        addDangerousMethod("java.lang.Class.newInstance", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("java.lang.reflect.Constructor.newInstance", RuleResult.RiskLevel.MEDIUM);
        
        // JNDI相关
        addDangerousMethod("javax.naming.InitialContext.lookup", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("javax.naming.Context.lookup", RuleResult.RiskLevel.HIGH);
        
        // 其他常见的gadget chain方法
        addDangerousMethod("java.util.PriorityQueue.readObject", RuleResult.RiskLevel.HIGH);
        addDangerousMethod("java.util.Comparator.compare", RuleResult.RiskLevel.MEDIUM);
        addDangerousMethod("sun.reflect.annotation.AnnotationInvocationHandler.readObject", RuleResult.RiskLevel.HIGH);
    }
    
    /**
     * 添加危险方法到黑名单
     */
    private void addDangerousMethod(String method, RuleResult.RiskLevel riskLevel) {
        dangerousDeserializationMethods.add(method);
        riskLevelMap.put(method, riskLevel);
    }
    
    @Override
    protected RuleResult doProcess(HookEvent event) {
        try {
            // 检查是否为RASP内部操作或框架启动操作
            if (isInternalOrFrameworkOperation(event)) {
                return RuleResult.allow(getName());
            }

            // 分析调用栈
            StackTraceAnalysisResult analysisResult = analyzeStackTrace(event);

            if (analysisResult.isDangerous()) {
                return handleDangerousDeserialization(event, analysisResult);
            }

            // 没有检测到危险的反序列化调用
            return RuleResult.allow(getName());

        } catch (Exception e) {
            logger.error("Error analyzing stack trace for deserialization detection: {}", event, e);
            return RuleResult.allow(getName());
        }
    }

    /**
     * 检查是否为RASP内部操作或框架启动操作
     */
    private boolean isInternalOrFrameworkOperation(HookEvent event) {
        StackTraceElement[] stackTrace = event.getStackTrace();
        if (stackTrace == null || stackTrace.length == 0) {
            return false;
        }

        // 检查调用栈中是否包含RASP内部调用
        for (StackTraceElement element : stackTrace) {
            String className = element.getClassName();
            String methodName = element.getMethodName();

            // RASP内部调用
            if (className.startsWith("com.rasp.")) {
                logger.debug("Skipping RASP internal operation: {}.{}", className, methodName);
                return true;
            }

            // Spring Boot Loader相关操作（应用启动时的正常文件读取）
            if (className.startsWith("org.springframework.boot.loader.")) {
                logger.debug("Skipping Spring Boot loader operation: {}.{}", className, methodName);
                return true;
            }

            // JVM内部类加载相关操作
            if (className.startsWith("java.lang.ClassLoader") ||
                className.startsWith("java.net.URLClassLoader") ||
                className.startsWith("sun.misc.Launcher") ||
                className.startsWith("jdk.internal.")) {
                logger.debug("Skipping JVM internal operation: {}.{}", className, methodName);
                return true;
            }
        }

        // 检查文件路径，如果是读取JAR文件或类文件，可能是正常的类加载操作
        if (event.getEventType() == HookEvent.EventType.FILE_READ) {
            String filePath = extractFilePath(event);

            if (filePath != null) {
                String lowerPath = filePath.toLowerCase();
                // 跳过JAR文件、类文件、库文件的读取
                if (lowerPath.endsWith(".jar") ||
                    lowerPath.endsWith(".class") ||
                    lowerPath.endsWith(".so") ||
                    lowerPath.endsWith(".dll") ||
                    lowerPath.contains("/lib/") ||
                    lowerPath.contains("\\lib\\")) {
                    logger.debug("Skipping system file read: {}", filePath);
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 从事件中提取文件路径
     */
    private String extractFilePath(HookEvent event) {
        try {
            // 尝试从事件属性中获取文件路径
            if (event.getAttributes() != null) {
                Object filePath = event.getAttributes().get("filePath");
                if (filePath != null) {
                    return filePath.toString();
                }
            }

            // 尝试通过反射获取文件路径（适用于FileReadEvent等）
            try {
                java.lang.reflect.Method getFilePathMethod = event.getClass().getMethod("getFilePath");
                Object result = getFilePathMethod.invoke(event);
                return result != null ? result.toString() : null;
            } catch (Exception ignored) {
                // 如果没有getFilePath方法，忽略
            }

        } catch (Exception e) {
            logger.debug("Failed to extract file path from event: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 分析调用栈
     */
    private StackTraceAnalysisResult analyzeStackTrace(HookEvent event) {
        StackTraceElement[] stackTrace = event.getStackTrace();
        if (stackTrace == null || stackTrace.length == 0) {
            return new StackTraceAnalysisResult();
        }
        
        StackTraceAnalysisResult result = new StackTraceAnalysisResult();
        
        // 遍历调用栈，查找危险方法
        for (int i = 0; i < stackTrace.length; i++) {
            StackTraceElement element = stackTrace[i];
            String className = element.getClassName();
            String methodName = element.getMethodName();
            String fullMethodName = className + "." + methodName;
            
            // 检查是否匹配黑名单
            if (dangerousDeserializationMethods.contains(fullMethodName)) {
                // 进一步验证是否真的是危险的反序列化调用
                if (isGenuineDeserializationThreat(stackTrace, i, fullMethodName)) {
                    result.setDangerous(true);
                    result.setDangerousMethod(fullMethodName);
                    result.setStackIndex(i);
                    result.setRiskLevel(riskLevelMap.getOrDefault(fullMethodName, RuleResult.RiskLevel.MEDIUM));

                    logger.debug("Found dangerous deserialization method in stack trace: {} at index {}",
                               fullMethodName, i);
                    break;
                } else {
                    logger.debug("Skipping false positive for method: {} (not a genuine threat)", fullMethodName);
                }
            }
        }
        
        return result;
    }

    /**
     * 验证是否为真正的反序列化威胁
     * 通过分析调用栈上下文来减少误报
     */
    private boolean isGenuineDeserializationThreat(StackTraceElement[] stackTrace, int dangerousMethodIndex, String dangerousMethod) {
        // 对于某些高风险方法，直接认为是威胁
        if (dangerousMethod.contains("InvokerTransformer.transform") ||
            dangerousMethod.contains("InstantiateTransformer.transform") ||
            dangerousMethod.contains("fastjson.JSON.parseObject") ||
            dangerousMethod.contains("ObjectInputStream.readObject")) {
            return true;
        }

        // 检查调用栈上下文，寻找反序列化的证据
        for (int i = dangerousMethodIndex + 1; i < Math.min(stackTrace.length, dangerousMethodIndex + 10); i++) {
            String className = stackTrace[i].getClassName();
            String methodName = stackTrace[i].getMethodName();

            // 寻找明确的反序列化上下文
            if (className.contains("ObjectInputStream") ||
                className.contains("fastjson") ||
                className.contains("jackson") ||
                className.contains("XStream") ||
                className.contains("Kryo") ||
                className.contains("Hessian") ||
                methodName.equals("readObject") ||
                methodName.equals("parseObject") ||
                methodName.equals("fromXML")) {
                return true;
            }

            // 如果发现是Web请求处理或正常业务逻辑，可能不是反序列化攻击
            if (className.contains("springframework") ||
                className.contains("servlet") ||
                className.contains("tomcat") ||
                className.contains("jetty")) {
                // 继续检查，不直接返回false
                continue;
            }
        }

        // 检查是否在HTTP请求上下文中
        // 如果不在HTTP请求中，反序列化攻击的可能性较低
        boolean hasHttpContext = false;
        for (StackTraceElement element : stackTrace) {
            String className = element.getClassName();
            if (className.contains("servlet") ||
                className.contains("spring") ||
                className.contains("controller") ||
                className.contains("http")) {
                hasHttpContext = true;
                break;
            }
        }

        // 如果没有HTTP上下文，且是常见的反射调用，可能是误报
        if (!hasHttpContext && dangerousMethod.contains("Method.invoke")) {
            return false;
        }

        // 默认情况下，如果找到了黑名单方法，认为是潜在威胁
        return true;
    }

    /**
     * 处理检测到的危险反序列化
     */
    private RuleResult handleDangerousDeserialization(HookEvent event, StackTraceAnalysisResult analysisResult) {
        String dangerousMethod = analysisResult.getDangerousMethod();
        RuleResult.RiskLevel riskLevel = analysisResult.getRiskLevel();
        
        // 构建告警信息
        String alertMessage = buildAlertMessage(event, analysisResult);
        
        // 记录安全告警
        logSecurityAlert(event, analysisResult, alertMessage);
        
        // 根据风险等级和配置决定是否阻断
        if (shouldBlock(riskLevel)) {
            logger.warn("BLOCKING potential deserialization attack: {} -> {} (risk: {})",
                       event.getEventType(), dangerousMethod, riskLevel);

            return RuleResult.block(getName(), riskLevel, alertMessage);
        } else {
            logger.info("DETECTED potential deserialization attack (not blocking): {} -> {} (risk: {})",
                       event.getEventType(), dangerousMethod, riskLevel);

            return RuleResult.log(getName(), riskLevel, alertMessage);
        }
    }
    
    /**
     * 构建告警信息
     */
    private String buildAlertMessage(HookEvent event, StackTraceAnalysisResult analysisResult) {
        StringBuilder message = new StringBuilder();
        message.append("Potential Deserialization Attack Detected\n");
        message.append("Trigger Event: ").append(event.getEventType()).append("\n");
        message.append("Dangerous Method: ").append(analysisResult.getDangerousMethod()).append("\n");
        message.append("Risk Level: ").append(analysisResult.getRiskLevel()).append("\n");
        message.append("Stack Position: ").append(analysisResult.getStackIndex()).append("\n");
        message.append("Target Class: ").append(event.getClassName()).append("\n");
        message.append("Target Method: ").append(event.getMethodName()).append("\n");
        message.append("Thread: ").append(event.getThreadName()).append(" (").append(event.getThreadId()).append(")\n");
        message.append("Timestamp: ").append(new Date(event.getTimestamp())).append("\n");
        
        // 添加具体的触发信息
        if (event instanceof CommandExecutionEvent) {
            CommandExecutionEvent cmdEvent = (CommandExecutionEvent) event;
            message.append("Command: ").append(cmdEvent.getCommand()).append("\n");
        } else if (event instanceof FileWriteEvent) {
            FileWriteEvent fileEvent = (FileWriteEvent) event;
            message.append("File Path: ").append(fileEvent.getFilePath()).append("\n");
        } else if (event instanceof FileReadEvent) {
            FileReadEvent fileEvent = (FileReadEvent) event;
            message.append("File Path: ").append(fileEvent.getFilePath()).append("\n");
        }
        
        // 添加调用栈摘要
        StackTraceElement[] stackTrace = event.getStackTrace();
        if (stackTrace != null && stackTrace.length > analysisResult.getStackIndex()) {
            message.append("Call Stack Summary:\n");
            int startIndex = analysisResult.getStackIndex();
            int maxLines = Math.min(5, stackTrace.length - startIndex);
            for (int i = startIndex; i < startIndex + maxLines; i++) {
                message.append("  ").append(stackTrace[i].toString()).append("\n");
            }
        }
        
        return message.toString();
    }
    
    /**
     * 记录安全告警
     */
    private void logSecurityAlert(HookEvent event, StackTraceAnalysisResult analysisResult, String alertMessage) {
        Logger securityLogger = LoggerFactory.getLogger("SECURITY." + getName());

        securityLogger.warn("=== POTENTIAL DESERIALIZATION ATTACK ===");
        securityLogger.warn("Trigger: {} -> {}", event.getEventType(), analysisResult.getDangerousMethod());
        securityLogger.warn("Risk Level: {}", analysisResult.getRiskLevel());
        securityLogger.warn("Thread: {} ({})", event.getThreadName(), event.getThreadId());
        securityLogger.warn("Details:\n{}", alertMessage);
        securityLogger.warn("=== END ALERT ===");
    }
    
    /**
     * 判断是否应该阻断
     */
    private boolean shouldBlock(RuleResult.RiskLevel riskLevel) {
        if (!blockingEnabled) {
            return false;
        }
        
        // 根据风险等级判断
        switch (minBlockRiskLevel) {
            case LOW:
                return true;
            case MEDIUM:
                return riskLevel != RuleResult.RiskLevel.LOW;
            case HIGH:
                return riskLevel == RuleResult.RiskLevel.HIGH || 
                       riskLevel == RuleResult.RiskLevel.CRITICAL;
            case CRITICAL:
                return riskLevel == RuleResult.RiskLevel.CRITICAL;
            default:
                return false;
        }
    }
    
    /**
     * 调用栈分析结果
     */
    private static class StackTraceAnalysisResult {
        private boolean dangerous = false;
        private String dangerousMethod;
        private int stackIndex = -1;
        private RuleResult.RiskLevel riskLevel = RuleResult.RiskLevel.LOW;
        
        public boolean isDangerous() {
            return dangerous;
        }
        
        public void setDangerous(boolean dangerous) {
            this.dangerous = dangerous;
        }
        
        public String getDangerousMethod() {
            return dangerousMethod;
        }
        
        public void setDangerousMethod(String dangerousMethod) {
            this.dangerousMethod = dangerousMethod;
        }
        
        public int getStackIndex() {
            return stackIndex;
        }
        
        public void setStackIndex(int stackIndex) {
            this.stackIndex = stackIndex;
        }
        
        public RuleResult.RiskLevel getRiskLevel() {
            return riskLevel;
        }
        
        public void setRiskLevel(RuleResult.RiskLevel riskLevel) {
            this.riskLevel = riskLevel;
        }
    }
    
    /**
     * 设置阻断模式
     */
    public void setBlockingEnabled(boolean blockingEnabled) {
        this.blockingEnabled = blockingEnabled;
        logger.info("DeserializationStackTraceRule blocking mode set to: {}", blockingEnabled);
    }
    
    /**
     * 设置最低阻断风险等级
     */
    public void setMinBlockRiskLevel(RuleResult.RiskLevel minBlockRiskLevel) {
        this.minBlockRiskLevel = minBlockRiskLevel;
        logger.info("DeserializationStackTraceRule minimum block risk level set to: {}", minBlockRiskLevel);
    }
    
    /**
     * 添加自定义危险方法
     */
    public void addCustomDangerousMethod(String method, RuleResult.RiskLevel riskLevel) {
        dangerousDeserializationMethods.add(method);
        riskLevelMap.put(method, riskLevel);
        logger.info("Added custom dangerous method: {} (risk: {})", method, riskLevel);
    }
    
    /**
     * 获取黑名单统计信息
     */
    public Map<String, Object> getBlacklistStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalMethods", dangerousDeserializationMethods.size());
        stats.put("blockingEnabled", blockingEnabled);
        stats.put("minBlockRiskLevel", minBlockRiskLevel);
        
        // 按风险等级统计
        Map<RuleResult.RiskLevel, Integer> riskStats = new HashMap<>();
        for (RuleResult.RiskLevel level : riskLevelMap.values()) {
            riskStats.put(level, riskStats.getOrDefault(level, 0) + 1);
        }
        stats.put("riskLevelDistribution", riskStats);
        
        return stats;
    }
}
